# Nova Proxy

A modular HTTP proxy service with load balancing, field mapping, and debug capabilities.

## Project Structure

```
nova-proxy/
├── main.go                     # Application entry point
├── config.json                 # Service configuration
├── internal/
│   ├── config/                 # Configuration management
│   │   └── loader.go           # Config loading and validation
│   ├── handlers/               # HTTP request handlers
│   │   ├── admin.go            # Admin endpoints
│   │   └── proxy.go            # Proxy request handling
│   ├── models/                 # Data structures
│   │   ├── requests.go         # Request models
│   │   └── upstream.go         # Upstream and service config models
│   ├── services/               # Business logic
│   │   ├── balancer.go         # Load balancing logic
│   │   ├── debug.go            # Debug functionality
│   │   └── proxy.go            # Proxy operations
│   └── utils/                  # Utility functions
│       ├── file.go             # File handling utilities
│       └── request.go          # Request processing utilities
└── go.mod                      # Go module definition
```

## Components

### Models (`internal/models/`)
- **upstream.go**: Defines `Upstream` and `ServiceConfig` structures
- **requests.go**: Request structures for different services (OCR, Liveness, Facematch)

### Services (`internal/services/`)
- **balancer.go**: Load balancing with weighted random selection
- **proxy.go**: HTTP proxy operations and request body preparation
- **debug.go**: Debug response generation and file content formatting

### Handlers (`internal/handlers/`)
- **proxy.go**: HTTP request handling for proxy operations
- **admin.go**: Administrative endpoints for configuration management

### Utils (`internal/utils/`)
- **file.go**: Binary content detection and file formatting
- **request.go**: Request structure utilities and field mapping

### Config (`internal/config/`)
- **loader.go**: Configuration loading, validation, and management

## Features

- **Load Balancing**: Weighted random selection across multiple upstreams
- **Field Mapping**: Dynamic field name mapping between client and upstream
- **Format Support**: JSON and multipart/form-data formats
- **Debug Mode**: Comprehensive debug information with binary file handling
- **Dynamic Configuration**: Runtime configuration updates via admin API
- **Modular Architecture**: Clean separation of concerns for easy maintenance

## API Endpoints

### Service Proxy
- `POST /:service` - Proxy requests to configured upstreams

### Admin API
- `POST /admin/config` - Update service configuration
- `GET /admin/config` - Get current configuration
- `GET /admin/status` - Get service status information

## Configuration

Services are configured in `config.json` with the following structure:

```json
{
  "service_name": [
    {
      "url": "http://upstream:port/path",
      "format": "json|multipart/form-data",
      "weight": 1,
      "field_mapping": {
        "client_field": "upstream_field"
      },
      "hardcoded_fields": {
        "api_key": "secret"
      },
      "debug": true
    }
  ]
}
```

## Building and Running

```bash
# Build the application
go build -o nova-proxy .

# Run the application
./nova-proxy
```

The server will start on port 8080 by default.

## Debug Mode

When `debug: true` is set for an upstream, the proxy will return detailed debug information instead of forwarding the request. This includes:

- Original request fields with file content info
- Mapped field transformations
- Request body sample (with binary content detection)
- Selected upstream information
- Headers and configuration details

Binary files are displayed as `FILE_CONTENT:xxx bytes` to avoid cluttering debug output.
