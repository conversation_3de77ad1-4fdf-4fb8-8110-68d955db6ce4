{"ocr": [{"url": "http://upstream1:8080/ocr", "format": "multipart/form-data", "weight": 3, "field_mapping": {"img1": "anh1", "img2": "anh2"}, "hardcoded_fields": {"access_key": "ak1_secret", "api_version": "v1"}, "debug": true}, {"url": "http://upstream2:8080/ocr", "format": "json", "weight": 1, "field_mapping": {"img1": "image1_base64", "img2": "image2_base64"}, "hardcoded_fields": {"api_key": "sk_live_123", "secret_key": "secret456", "service_type": "ocr"}, "debug": true}], "liveness": [{"url": "http://upstream1:8081/liveness", "format": "json", "weight": 2, "field_mapping": {"img1": "image1_b64", "img2": "image2_b64", "img3": "image3_b64"}, "hardcoded_fields": {"access_key": "liveness_ak", "device_id": "default_device"}, "debug": true}], "facematch": [{"url": "http://upstream3:8082/facematch", "format": "multipart/form-data", "weight": 1, "field_mapping": {"img1": "face1", "img2": "face2"}, "hardcoded_fields": {"api_key": "face_api_key"}, "debug": true}]}