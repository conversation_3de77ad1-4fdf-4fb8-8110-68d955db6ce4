{"ocr": {"partners": [{"name": "partner-a", "business_codes": ["banking", "finance"], "response_mapping": {"success_field": "status", "success_values": ["success", "ok", 200], "data_field": "result", "error_field": "error", "error_code_field": "error.code", "message_field": "error.message", "confidence_field": "result.confidence", "field_mappings": {"result.document_type": "document_type", "result.extracted_text": "raw_text", "result.fields.name": "name", "result.fields.id_number": "id_number", "result.fields.date_of_birth": "date_of_birth", "result.fields.address": "address"}}, "upstreams": [{"url": "http://partner-a.example.com/ocr", "format": "json", "weight": 10, "field_mapping": {"img1": "image_1", "img2": "image_2"}, "hardcoded_fields": {"api_key": "partner-a-key"}, "region": "us-east-1", "environment": "production", "debug": true}, {"url": "http://partner-a-backup.example.com/ocr", "format": "json", "weight": 10, "field_mapping": {"img1": "image_1", "img2": "image_2"}, "hardcoded_fields": {"api_key": "partner-a-backup-key"}, "region": "us-west-2", "environment": "production", "debug": true}]}, {"name": "partner-b", "business_codes": ["insurance", "healthcare"], "response_mapping": {"success_field": "success", "success_values": [true], "data_field": "data", "error_field": "error_message", "confidence_field": "confidence_score", "field_mappings": {"doc_type": "document_type", "text_content": "raw_text", "extracted_data.full_name": "name", "extracted_data.identification": "id_number", "extracted_data.birth_date": "date_of_birth", "extracted_data.residence": "address"}}, "upstreams": [{"url": "http://partner-b.example.com/api/ocr", "format": "multipart/form-data", "weight": 50, "field_mapping": {"img1": "document_front", "img2": "document_back"}, "region": "eu-west-1", "environment": "production"}]}, {"name": "partner-c", "business_codes": ["retail", "ecommerce"], "response_mapping": {"success_field": "code", "success_values": [0, "0"], "data_field": "payload", "error_code_field": "code", "message_field": "message", "confidence_field": "payload.accuracy", "field_mappings": {"payload.type": "document_type", "payload.content": "raw_text", "payload.person.name": "name", "payload.person.id": "id_number", "payload.person.dob": "date_of_birth"}}, "upstreams": [{"url": "http://partner-c.example.com/ocr/process", "format": "json", "weight": 20, "region": "ap-southeast-1", "environment": "production"}]}]}, "liveness": {"partners": [{"name": "partner-a", "business_codes": ["banking", "finance"], "response_mapping": {"success_field": "status", "success_values": ["success"], "data_field": "result", "confidence_field": "result.confidence", "field_mappings": {"result.is_alive": "is_live", "result.liveness_score": "score"}}, "upstreams": [{"url": "http://partner-a.example.com/liveness", "format": "json", "weight": 40, "region": "us-east-1", "environment": "production"}]}, {"name": "partner-d", "business_codes": ["insurance", "kyc"], "response_mapping": {"success_field": "success", "success_values": [true], "confidence_field": "confidence", "field_mappings": {"live": "is_live", "score": "score"}}, "upstreams": [{"url": "http://partner-d.example.com/api/liveness-check", "format": "multipart/form-data", "weight": 60, "region": "eu-central-1", "environment": "production"}]}]}, "facematch": {"partners": [{"name": "partner-b", "business_codes": ["all"], "response_mapping": {"success_field": "status", "success_values": ["ok"], "data_field": "match_result", "confidence_field": "match_result.confidence", "field_mappings": {"match_result.is_match": "is_match", "match_result.similarity_score": "similarity", "match_result.match_score": "score"}}, "upstreams": [{"url": "http://partner-b.example.com/api/face-match", "format": "json", "weight": 100, "debug": true, "region": "us-east-1", "environment": "production"}]}]}}