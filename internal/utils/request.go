package utils

import (
	"mime/multipart"
	"reflect"

	"nova-proxy/internal/models"
)

// GetRequestStruct returns a pointer to the appropriate request struct for the given service
func GetRequestStruct(service string) interface{} {
	switch service {
	case "ocr":
		return &models.OcrRequest{}
	case "liveness":
		return &models.LivenessRequest{}
	case "facematch":
		return &models.FacematchRequest{}
	default:
		return nil
	}
}

// ApplyMappingToReq applies field mapping to request struct using reflection
func ApplyMappingToReq(req interface{}, mapping map[string]string) map[string]interface{} {
	v := reflect.ValueOf(req).Elem()
	data := make(map[string]interface{})

	for i := 0; i < v.NumField(); i++ {
		field := v.Type().Field(i)
		value := v.Field(i)
		formTag := field.Tag.Get("form")
		if formTag == "" {
			continue
		}

		newKey := mapping[formTag]
		if newKey == "" {
			newKey = formTag
		}

		switch value.Kind() {
		case reflect.Ptr: // FileHeader
			if fh, ok := value.Interface().(*multipart.FileHeader); ok && fh != nil {
				data[newKey] = fh
			}
		case reflect.String:
			if str := value.String(); str != "" {
				data[newKey] = str
			}
		}
	}
	return data
}
