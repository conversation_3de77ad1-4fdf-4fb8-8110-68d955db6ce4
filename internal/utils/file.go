package utils

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"strings"
	"unicode/utf8"
)

// IsBinaryContent checks if the content appears to be binary
func IsBinaryContent(data []byte) bool {
	// Check for null bytes or non-UTF8 content
	if bytes.Contains(data, []byte{0}) {
		return true
	}

	// Check if content is valid UTF-8
	if !utf8.Valid(data) {
		return true
	}

	// Check for common binary file signatures
	if len(data) >= 4 {
		// Check for common binary file headers
		header := data[:4]
		binarySignatures := [][]byte{
			{0xFF, 0xD8, 0xFF},       // JPEG
			{0x89, 0x50, 0x4E, 0x47}, // PNG
			{0x47, 0x49, 0x46},       // GIF
			{0x25, 0x50, 0x44, 0x46}, // PDF
			{0x50, 0x4B, 0x03, 0x04}, // ZIP
		}

		for _, sig := range binarySignatures {
			if bytes.HasPrefix(header, sig) {
				return true
			}
		}
	}

	return false
}

// FormatFileContent returns a formatted string for file content in debug mode
func FormatFileContent(fh *multipart.FileHeader) string {
	if fh == nil {
		return "nil"
	}

	// Try to read a small sample to check if it's binary
	f, err := fh.Open()
	if err != nil {
		return fmt.Sprintf("FILE_CONTENT:%d bytes (error reading: %v)", fh.Size, err)
	}
	defer f.Close()

	// Read first 512 bytes to check content type
	sample := make([]byte, 512)
	n, _ := f.Read(sample)
	sample = sample[:n]

	if IsBinaryContent(sample) {
		return fmt.Sprintf("FILE_CONTENT:%d bytes", fh.Size)
	}

	// If it's text content and small enough, show a preview
	if fh.Size <= 1024 {
		f.Seek(0, 0) // Reset to beginning
		content, err := io.ReadAll(f)
		if err != nil {
			return fmt.Sprintf("FILE_CONTENT:%d bytes (error reading: %v)", fh.Size, err)
		}
		return fmt.Sprintf("TEXT_CONTENT:%d bytes: %s", fh.Size, strings.TrimSpace(string(content)))
	}

	return fmt.Sprintf("TEXT_CONTENT:%d bytes (too large to preview)", fh.Size)
}
