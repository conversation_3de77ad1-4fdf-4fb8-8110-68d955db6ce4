package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"
	"nova-proxy/internal/utils"

	"github.com/gin-gonic/gin"
)

// Proxy<PERSON>andler handles HTTP proxy requests
type ProxyH<PERSON>ler struct {
	balancer     *services.Balancer
	proxyService *services.ProxyService
	debugService *services.DebugService
	normalizer   *services.ResponseNormalizer
}

// NewProxyHandler creates a new proxy handler
func NewProxyHandler(balancer *services.Balancer, proxyService *services.ProxyService, debugService *services.DebugService) *ProxyHandler {
	return &ProxyHandler{
		balancer:     balancer,
		proxyService: proxyService,
		debugService: debugService,
		normalizer:   services.NewResponseNormalizer(),
	}
}

// HandleProxy handles incoming proxy requests
func (h *ProxyHandler) HandleProxy(c *gin.Context) {
	service := c.<PERSON>m("service")

	// Extract routing headers
	partnerName := c.<PERSON>er("x-partner")
	businessCode := c.<PERSON>er("x-biz")

	// Get upstream using intelligent routing
	upstream, partner := h.balancer.GetUpstream(service, partnerName, businessCode)
	if upstream == nil {
		c.JSON(404, gin.H{"error": "No upstream found"})
		return
	}

	// Bind and validate request
	reqStruct := utils.GetRequestStruct(service)
	if reqStruct == nil {
		c.JSON(400, gin.H{"error": "Invalid service"})
		return
	}

	if err := c.ShouldBind(reqStruct); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Apply field mapping
	mappedData := utils.ApplyMappingToReq(reqStruct, upstream.FieldMapping)

	// Handle debug mode
	if upstream.Debug {
		h.handleDebugMode(c, service, reqStruct, upstream, partner, mappedData)
		return
	}

	// Handle normal proxy mode
	h.handleProxyMode(c, upstream, partner, mappedData)
}

// handleDebugMode processes requests in debug mode
func (h *ProxyHandler) handleDebugMode(c *gin.Context, service string, reqStruct interface{}, upstream *models.Upstream, partner *models.Partner, mappedData map[string]interface{}) {
	var body io.Reader
	var contentType string
	var err error

	// Build body for debug sample
	if upstream.Format == "json" {
		jsonData, err := h.debugService.BuildJSONFromReqDebug(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		jsonBytes, _ := json.Marshal(jsonData)
		body = bytes.NewReader(jsonBytes)
		contentType = "application/json"
	} else {
		body, contentType, err = h.proxyService.BuildMultipartFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
	}

	// Return debug response with routing info
	debugResp := h.debugService.BuildDebugResponse(c, service, reqStruct, upstream, mappedData, contentType, body)

	// Add routing information to debug response
	if upstreamInfo, exists := debugResp["selected_upstream"].(gin.H); exists {
		upstreamInfo["partner_name"] = partner.Name
		upstreamInfo["business_codes"] = partner.BusinessCodes
		upstreamInfo["upstream_url"] = upstream.URL
		upstreamInfo["upstream_region"] = upstream.Region
		upstreamInfo["upstream_environment"] = upstream.Environment

		// Determine routing reason
		routingReason := "weighted round-robin"
		headerPartnerName := c.GetHeader("x-partner")
		headerBusinessCode := c.GetHeader("x-biz")

		if headerPartnerName != "" {
			routingReason = "x-partner header"
		} else if headerBusinessCode != "" {
			routingReason = "x-biz header"
		}
		upstreamInfo["routing_reason"] = routingReason
		upstreamInfo["routing_headers"] = gin.H{
			"x-partner": headerPartnerName,
			"x-biz":     headerBusinessCode,
		}
	}

	c.JSON(200, debugResp)
}

// handleProxyMode processes requests in normal proxy mode
func (h *ProxyHandler) handleProxyMode(c *gin.Context, upstream *models.Upstream, partner *models.Partner, mappedData map[string]interface{}) {
	service := c.Param("service")
	startTime := time.Now()

	// Prepare request body
	body, contentType, err := h.proxyService.PrepareRequestBody(upstream, mappedData)
	if err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Create and configure proxy request
	req, err := http.NewRequest(c.Request.Method, upstream.URL, body)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to create request"})
		return
	}
	req.Header.Set("Content-Type", contentType)

	// Copy headers from original request
	for key, values := range c.Request.Header {
		if key != "Content-Length" {
			for _, value := range values {
				req.Header.Add(key, value)
			}
		}
	}

	// Execute request to partner
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to contact partner"})
		return
	}
	defer resp.Body.Close()

	// Read partner response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to read partner response"})
		return
	}

	// Generate request ID for tracking
	requestID := fmt.Sprintf("%s-%d", service, time.Now().UnixNano())

	// Normalize response if mapping is configured
	if partner.ResponseMapping != nil {
		normalizedResp, err := h.normalizer.NormalizeResponse(
			responseBody, upstream, partner, service, startTime, requestID)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to normalize response"})
			return
		}
		c.JSON(resp.StatusCode, normalizedResp)
		return
	}

	// Return raw response if no normalization configured
	c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), responseBody)
}
