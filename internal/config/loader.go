package config

import (
	"encoding/json"
	"fmt"
	"os"

	"nova-proxy/internal/models"
	"nova-proxy/internal/services"
)

// Loader handles configuration loading and management
type Loader struct {
	configPath string
}

// NewLoader creates a new configuration loader
func NewLoader(configPath string) *Loader {
	return &Loader{
		configPath: configPath,
	}
}

// LoadConfig loads configuration from file and applies it to the balancer
func (l *Loader) LoadConfig(balancer *services.Balancer) error {
	data, err := os.ReadFile(l.configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// Try to detect the configuration format
	if l.isNewFormat(data) {
		return l.loadNewFormat(data, balancer)
	} else {
		return l.loadLegacyFormat(data, balancer)
	}
}

// isNewFormat detects if the configuration uses the new partner-based format
func (l *Loader) isNewFormat(data []byte) bool {
	var testConfig map[string]interface{}
	if err := json.Unmarshal(data, &testConfig); err != nil {
		return false
	}

	// Check if any service has a "partners" field
	for _, serviceConfig := range testConfig {
		if serviceMap, ok := serviceConfig.(map[string]interface{}); ok {
			if _, hasPartners := serviceMap["partners"]; hasPartners {
				return true
			}
		}
	}
	return false
}

// loadNewFormat loads the new partner-based configuration format
func (l *Loader) loadNewFormat(data []byte, balancer *services.Balancer) error {
	var configs map[string]models.ServiceConfig
	if err := json.Unmarshal(data, &configs); err != nil {
		return fmt.Errorf("failed to unmarshal new format config: %w", err)
	}

	for service, config := range configs {
		fmt.Printf("Loaded config for %s with %d partners\n", service, len(config.Partners))
		balancer.LoadConfig(service, &config)
	}

	return nil
}

// loadLegacyFormat loads the old upstream-based configuration format
func (l *Loader) loadLegacyFormat(data []byte, balancer *services.Balancer) error {
	var rawConfigs map[string][]models.Upstream
	if err := json.Unmarshal(data, &rawConfigs); err != nil {
		return fmt.Errorf("failed to unmarshal legacy config: %w", err)
	}

	// Convert old format to new Partner-based format and load into balancer
	for service, upstreams := range rawConfigs {
		// Convert old upstream format to partner format for backward compatibility
		partners := l.convertUpstreamsToPartners(upstreams)
		cfg := &models.ServiceConfig{
			Partners: partners,
		}
		fmt.Printf("Loaded legacy config for %s with %d partners (converted from %d upstreams)\n",
			service, len(cfg.Partners), len(upstreams))
		balancer.LoadConfig(service, cfg)
	}

	return nil
}

// LoadConfigFromData loads configuration from raw data
func (l *Loader) LoadConfigFromData(data []byte, balancer *services.Balancer) error {
	// Try to detect the configuration format
	if l.isNewFormat(data) {
		return l.loadNewFormat(data, balancer)
	} else {
		return l.loadLegacyFormat(data, balancer)
	}
}

// convertUpstreamsToPartners converts old upstream format to new partner format for backward compatibility
func (l *Loader) convertUpstreamsToPartners(upstreams []models.Upstream) []models.Partner {
	partners := make([]models.Partner, len(upstreams))

	for i, upstream := range upstreams {
		partners[i] = models.Partner{
			Name:      fmt.Sprintf("legacy-partner-%d", i),
			Upstreams: []models.Upstream{upstream},
		}
	}

	return partners
}

// ValidateConfig validates the configuration structure
func (l *Loader) ValidateConfig(configData map[string]models.ServiceConfig) error {
	for service, config := range configData {
		if len(config.Partners) == 0 {
			return fmt.Errorf("service %s has no partners configured", service)
		}

		for i, partner := range config.Partners {
			if partner.Name == "" {
				return fmt.Errorf("service %s partner %d has empty name", service, i)
			}
			if len(partner.Upstreams) == 0 {
				return fmt.Errorf("service %s partner %s has no upstreams", service, partner.Name)
			}

			for j, upstream := range partner.Upstreams {
				if upstream.URL == "" {
					return fmt.Errorf("service %s partner %s upstream %d has empty URL", service, partner.Name, j)
				}
				if upstream.Weight <= 0 {
					return fmt.Errorf("service %s partner %s upstream %d has invalid weight: %d", service, partner.Name, j, upstream.Weight)
				}
				if upstream.Format != "json" && upstream.Format != "multipart/form-data" {
					return fmt.Errorf("service %s partner %s upstream %d has unsupported format: %s", service, partner.Name, j, upstream.Format)
				}
			}
		}
	}

	return nil
}
