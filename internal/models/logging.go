package models

import (
	"time"
)

// LogLevel represents the severity level of a log entry
type LogLevel string

const (
	LogLevelInfo  LogLevel = "INFO"
	LogLevelWarn  LogLevel = "WARN"
	LogLevelError LogLevel = "ERROR"
	LogLevelDebug LogLevel = "DEBUG"
)

// RequestLogEntry represents a logged HTTP request
type RequestLogEntry struct {
	ID          string            `json:"id"`
	Timestamp   time.Time         `json:"timestamp"`
	Service     string            `json:"service"`
	Method      string            `json:"method"`
	URL         string            `json:"url"`
	Headers     map[string]string `json:"headers,omitempty"`
	ContentType string            `json:"content_type,omitempty"`
	BodySize    int64             `json:"body_size"`
	BodySample  string            `json:"body_sample,omitempty"` // Truncated for large bodies
	ClientIP    string            `json:"client_ip"`
	UserAgent   string            `json:"user_agent,omitempty"`
}

// ResponseLogEntry represents a logged HTTP response
type ResponseLogEntry struct {
	ID           string            `json:"id"`
	RequestID    string            `json:"request_id"`
	Timestamp    time.Time         `json:"timestamp"`
	StatusCode   int               `json:"status_code"`
	Headers      map[string]string `json:"headers,omitempty"`
	ContentType  string            `json:"content_type,omitempty"`
	BodySize     int64             `json:"body_size"`
	BodySample   string            `json:"body_sample,omitempty"` // Truncated for large bodies
	Duration     time.Duration     `json:"duration"`
	UpstreamURL  string            `json:"upstream_url"`
	Error        string            `json:"error,omitempty"`
}

// ProxyLogEntry represents a complete proxy transaction
type ProxyLogEntry struct {
	ID          string            `json:"id"`
	Timestamp   time.Time         `json:"timestamp"`
	Service     string            `json:"service"`
	ClientIP    string            `json:"client_ip"`
	Method      string            `json:"method"`
	RequestURL  string            `json:"request_url"`
	UpstreamURL string            `json:"upstream_url"`
	StatusCode  int               `json:"status_code"`
	Duration    time.Duration     `json:"duration"`
	RequestSize int64             `json:"request_size"`
	ResponseSize int64            `json:"response_size"`
	Error       string            `json:"error,omitempty"`
	Debug       bool              `json:"debug"`
	Metadata    map[string]string `json:"metadata,omitempty"`
}

// LogEntry represents a generic log entry
type LogEntry struct {
	ID        string                 `json:"id"`
	Timestamp time.Time              `json:"timestamp"`
	Level     LogLevel               `json:"level"`
	Message   string                 `json:"message"`
	Service   string                 `json:"service,omitempty"`
	Data      map[string]interface{} `json:"data,omitempty"`
}

// LogConfig represents logging configuration
type LogConfig struct {
	Enabled          bool          `json:"enabled"`
	Level            LogLevel      `json:"level"`
	Directory        string        `json:"directory"`
	MaxFileSize      int64         `json:"max_file_size"`      // in bytes
	MaxFiles         int           `json:"max_files"`          // number of files to keep
	FlushInterval    time.Duration `json:"flush_interval"`     // how often to write to disk
	BufferSize       int           `json:"buffer_size"`        // channel buffer size
	IncludeHeaders   bool          `json:"include_headers"`
	IncludeBody      bool          `json:"include_body"`
	MaxBodySample    int           `json:"max_body_sample"`    // max bytes to sample from body
	CompressOldLogs  bool          `json:"compress_old_logs"`
	LogRequests      bool          `json:"log_requests"`
	LogResponses     bool          `json:"log_responses"`
	LogProxyEvents   bool          `json:"log_proxy_events"`
}
