package models

import "time"

// StandardResponse represents the normalized response format for all services
type StandardResponse struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message,omitempty"`
	Data      interface{}            `json:"data,omitempty"`
	Error     *ErrorDetails          `json:"error,omitempty"`
	Metadata  *ResponseMetadata      `json:"metadata,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// ErrorDetails provides structured error information
type ErrorDetails struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// ResponseMetadata contains additional response information
type ResponseMetadata struct {
	PartnerID     string  `json:"partner_id"`
	ProcessingTime float64 `json:"processing_time_ms"`
	RequestID     string  `json:"request_id,omitempty"`
	Version       string  `json:"version,omitempty"`
}

// OCRResponse represents standardized OCR service response
type OCRResponse struct {
	DocumentType string                 `json:"document_type"`
	Confidence   float64                `json:"confidence"`
	Fields       map[string]interface{} `json:"fields"`
	RawText      string                 `json:"raw_text,omitempty"`
}

// LivenessResponse represents standardized liveness detection response
type LivenessResponse struct {
	IsLive     bool    `json:"is_live"`
	Confidence float64 `json:"confidence"`
	Score      float64 `json:"score"`
	Checks     []LivenessCheck `json:"checks,omitempty"`
}

// LivenessCheck represents individual liveness verification checks
type LivenessCheck struct {
	Type       string  `json:"type"`
	Passed     bool    `json:"passed"`
	Confidence float64 `json:"confidence"`
	Details    string  `json:"details,omitempty"`
}

// FaceMatchResponse represents standardized face matching response
type FaceMatchResponse struct {
	IsMatch    bool    `json:"is_match"`
	Confidence float64 `json:"confidence"`
	Score      float64 `json:"score"`
	Similarity float64 `json:"similarity"`
}

// ResponseMapping defines how to map partner responses to standard format
type ResponseMapping struct {
	SuccessField    string            `json:"success_field"`
	MessageField    string            `json:"message_field"`
	DataField       string            `json:"data_field"`
	ErrorField      string            `json:"error_field"`
	FieldMappings   map[string]string `json:"field_mappings"`
	SuccessValues   []interface{}     `json:"success_values"`
	ErrorCodeField  string            `json:"error_code_field"`
	ConfidenceField string            `json:"confidence_field"`
}
