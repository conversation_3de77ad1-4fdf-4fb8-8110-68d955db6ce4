package models

import "mime/multipart"

// OcrRequest represents OCR service request structure
type OcrRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
}

// LivenessRequest represents liveness detection service request structure
type LivenessRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
	Img3 *multipart.FileHeader `form:"img3" binding:"required"`
}

// FacematchRequest represents face matching service request structure
type FacematchRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
}
