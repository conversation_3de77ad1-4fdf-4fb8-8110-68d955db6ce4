package models

// Partner represents a partner configuration with multiple upstreams
type Partner struct {
	Name            string           `json:"name"`
	BusinessCodes   []string         `json:"business_codes,omitempty"`   // Business codes this partner handles
	ResponseMapping *ResponseMapping `json:"response_mapping,omitempty"` // Response normalization config
	Upstreams       []Upstream       `json:"upstreams"`                  // Multiple endpoints for this partner
}

// Upstream represents a single upstream server endpoint
type Upstream struct {
	URL             string            `json:"url"`
	Format          string            `json:"format"`
	Weight          int               `json:"weight"`
	FieldMapping    map[string]string `json:"field_mapping,omitempty"`
	HardcodedFields map[string]string `json:"hardcoded_fields,omitempty"`
	Debug           bool              `json:"debug,omitempty"`
	Region          string            `json:"region,omitempty"`      // Optional region identifier
	Environment     string            `json:"environment,omitempty"` // Optional environment (prod, staging, etc.)
}

// ServiceConfig represents configuration for a service with multiple partners
type ServiceConfig struct {
	Partners []Partner `json:"partners"`
}
