package models

// Upstream represents a single upstream server configuration
type Upstream struct {
	URL             string            `json:"url"`
	Format          string            `json:"format"`
	Weight          int               `json:"weight"`
	FieldMapping    map[string]string `json:"field_mapping,omitempty"`
	HardcodedFields map[string]string `json:"hardcoded_fields,omitempty"`
	Debug           bool              `json:"debug,omitempty"`
}

// ServiceConfig represents configuration for a service with multiple upstreams
type ServiceConfig struct {
	Upstreams []Upstream `json:"upstreams"`
}
