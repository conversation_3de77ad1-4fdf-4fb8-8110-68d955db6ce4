package services

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"reflect"

	"nova-proxy/internal/models"
	"nova-proxy/internal/utils"

	"github.com/gin-gonic/gin"
)

// DebugService handles debug-related functionality
type DebugService struct{}

// NewDebugService creates a new debug service instance
func NewDebugService() *DebugService {
	return &DebugService{}
}

// BuildDebugResponse creates a comprehensive debug response
func (d *DebugService) BuildDebugResponse(c *gin.Context, service string, reqStruct interface{}, upstream *models.Upstream, mappedData map[string]interface{}, contentType string, body io.Reader) gin.H {
	// Original fields (using reflection to extract)
	originalFields := make(map[string]interface{})
	v := reflect.ValueOf(reqStruct).Elem()
	for i := 0; i < v.NumField(); i++ {
		field := v.Type().Field(i)
		value := v.Field(i)
		formTag := field.Tag.Get("form")
		if formTag == "" {
			continue
		}
		switch value.Kind() {
		case reflect.Ptr: // FileHeader
			if fh, ok := value.Interface().(*multipart.FileHeader); ok && fh != nil {
				originalFields[formTag] = utils.FormatFileContent(fh)
			}
		case reflect.String:
			if str := value.String(); str != "" {
				originalFields[formTag] = str
			}
		}
	}

	// Built body sample
	bodySample := d.formatBodySample(body)

	// Multipart fields list (if format is multipart)
	var multipartFields []string
	if upstream.Format == "multipart/form-data" {
		for key := range mappedData {
			multipartFields = append(multipartFields, key)
		}
		for key := range upstream.HardcodedFields {
			multipartFields = append(multipartFields, key)
		}
	}

	// Format mapped fields for debug display
	debugMappedFields := make(map[string]interface{})
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			debugMappedFields[key] = utils.FormatFileContent(fh)
		} else {
			debugMappedFields[key] = val
		}
	}

	return gin.H{
		"debug_mode": true,
		"service":    service,
		"selected_upstream": gin.H{
			"url":    upstream.URL,
			"format": upstream.Format,
			"weight": upstream.Weight,
		},
		"original_request_fields": originalFields,
		"mapped_fields":           debugMappedFields,
		"hardcoded_fields":        upstream.HardcodedFields,
		"built_body_sample":       bodySample,
		"content_type":            contentType,
		"multipart_fields_list":   multipartFields,
		"proxy_url":               upstream.URL,
		"copied_headers":          c.Request.Header,
	}
}

// formatBodySample formats the body content for debug display
func (d *DebugService) formatBodySample(body io.Reader) string {
	if body == nil {
		return ""
	}

	if r, ok := body.(*bytes.Buffer); ok {
		data := r.Bytes()
		if utils.IsBinaryContent(data) {
			return fmt.Sprintf("BINARY_CONTENT:%d bytes", len(data))
		}
		content := r.String()
		if len(content) > 500 {
			return content[:497] + "... (truncated)"
		}
		return content
	}

	if r, ok := body.(*bytes.Reader); ok {
		// For JSON, read sample
		buf := new(bytes.Buffer)
		tee := io.TeeReader(r, buf)
		limited := io.LimitReader(tee, 500) // Truncate if large
		io.Copy(io.Discard, limited)
		data := buf.Bytes()
		if utils.IsBinaryContent(data) {
			return fmt.Sprintf("BINARY_CONTENT:%d+ bytes", len(data))
		}
		bodySample := buf.String()
		if len(bodySample) > 500 {
			return bodySample[:497] + "... (truncated)"
		}
		return bodySample
	}

	return fmt.Sprintf("Body type: %T (sample not readable)", body)
}

// BuildJSONFromReqDebug builds JSON for debug mode with file content info
func (d *DebugService) BuildJSONFromReqDebug(mappedData map[string]interface{}, hardcoded map[string]string) (interface{}, error) {
	jsonData := make(map[string]interface{})

	// Copy mapped data (show file info instead of base64)
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			jsonData[key] = utils.FormatFileContent(fh)
		} else if str, ok := val.(string); ok {
			jsonData[key] = str
		}
	}

	// Add hardcoded fields
	for key, val := range hardcoded {
		jsonData[key] = val
	}

	return jsonData, nil
}
