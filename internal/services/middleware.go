package services

import (
	"bytes"
	"crypto/rand"
	"fmt"
	"io"
	"strconv"
	"time"

	"nova-proxy/internal/models"
	"nova-proxy/internal/utils"

	"github.com/gin-gonic/gin"
)

// generateID generates a simple unique ID
func generateID() string {
	b := make([]byte, 16)
	rand.Read(b)
	return fmt.Sprintf("%x-%x-%x-%x-%x", b[0:4], b[4:6], b[6:8], b[8:10], b[10:16])
}

// LoggingMiddleware provides request/response logging middleware
type LoggingMiddleware struct {
	logger *AsyncLogger
	config *models.LogConfig
}

// NewLoggingMiddleware creates a new logging middleware
func NewLoggingMiddleware(logger *AsyncLogger, config *models.LogConfig) *LoggingMiddleware {
	return &LoggingMiddleware{
		logger: logger,
		config: config,
	}
}

// responseWriter wraps gin.ResponseWriter to capture response data
type responseWriter struct {
	gin.ResponseWriter
	body       *bytes.Buffer
	statusCode int
}

func (w *responseWriter) Write(data []byte) (int, error) {
	if w.body != nil && w.body.Len() < 10240 { // Limit buffer size
		w.body.Write(data)
	}
	return w.ResponseWriter.Write(data)
}

func (w *responseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

// RequestResponseLogger returns a middleware that logs requests and responses
func (m *LoggingMiddleware) RequestResponseLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !m.config.Enabled {
			c.Next()
			return
		}

		start := time.Now()
		requestID := generateID()

		// Capture request data
		var requestBody []byte
		if m.config.IncludeBody && c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// Set request ID in context
		c.Set("request_id", requestID)

		// Wrap response writer to capture response data
		responseBuffer := &bytes.Buffer{}
		wrappedWriter := &responseWriter{
			ResponseWriter: c.Writer,
			body:           responseBuffer,
			statusCode:     200, // default
		}
		c.Writer = wrappedWriter

		// Process request
		c.Next()

		// Calculate duration
		duration := time.Since(start)

		// Log request (async)
		go m.logRequest(c, requestID, requestBody, start)

		// Log response (async)
		go m.logResponse(c, requestID, wrappedWriter, duration)
	}
}

// logRequest logs the request data asynchronously
func (m *LoggingMiddleware) logRequest(c *gin.Context, requestID string, body []byte, timestamp time.Time) {
	if !m.config.LogRequests {
		return
	}

	headers := make(map[string]string)
	if m.config.IncludeHeaders {
		for key, values := range c.Request.Header {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}
	}

	bodySample := ""
	if m.config.IncludeBody && len(body) > 0 {
		if utils.IsBinaryContent(body) {
			bodySample = "BINARY_CONTENT"
		} else {
			maxSample := m.config.MaxBodySample
			if maxSample <= 0 {
				maxSample = 1024
			}
			if len(body) > maxSample {
				bodySample = string(body[:maxSample]) + "... (truncated)"
			} else {
				bodySample = string(body)
			}
		}
	}

	entry := &models.RequestLogEntry{
		ID:          requestID,
		Timestamp:   timestamp,
		Service:     c.Param("service"),
		Method:      c.Request.Method,
		URL:         c.Request.URL.String(),
		Headers:     headers,
		ContentType: c.Request.Header.Get("Content-Type"),
		BodySize:    int64(len(body)),
		BodySample:  bodySample,
		ClientIP:    c.ClientIP(),
		UserAgent:   c.Request.UserAgent(),
	}

	m.logger.LogRequest(entry)
}

// logResponse logs the response data asynchronously
func (m *LoggingMiddleware) logResponse(c *gin.Context, requestID string, writer *responseWriter, duration time.Duration) {
	if !m.config.LogResponses {
		return
	}

	headers := make(map[string]string)
	if m.config.IncludeHeaders {
		for key, values := range writer.Header() {
			if len(values) > 0 {
				headers[key] = values[0]
			}
		}
	}

	bodySample := ""
	responseBody := writer.body.Bytes()
	if m.config.IncludeBody && len(responseBody) > 0 {
		if utils.IsBinaryContent(responseBody) {
			bodySample = "BINARY_CONTENT"
		} else {
			maxSample := m.config.MaxBodySample
			if maxSample <= 0 {
				maxSample = 1024
			}
			if len(responseBody) > maxSample {
				bodySample = string(responseBody[:maxSample]) + "... (truncated)"
			} else {
				bodySample = string(responseBody)
			}
		}
	}

	// Get upstream URL from context if available
	upstreamURL := ""
	if url, exists := c.Get("upstream_url"); exists {
		if urlStr, ok := url.(string); ok {
			upstreamURL = urlStr
		}
	}

	// Get error from context if available
	errorMsg := ""
	if err, exists := c.Get("proxy_error"); exists {
		if errStr, ok := err.(string); ok {
			errorMsg = errStr
		}
	}

	entry := &models.ResponseLogEntry{
		ID:          generateID(),
		RequestID:   requestID,
		Timestamp:   time.Now(),
		StatusCode:  writer.statusCode,
		Headers:     headers,
		ContentType: writer.Header().Get("Content-Type"),
		BodySize:    int64(len(responseBody)),
		BodySample:  bodySample,
		Duration:    duration,
		UpstreamURL: upstreamURL,
		Error:       errorMsg,
	}

	m.logger.LogResponse(entry)
}

// ProxyEventLogger logs proxy events (service selection, errors, etc.)
func (m *LoggingMiddleware) ProxyEventLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !m.config.Enabled || !m.config.LogProxyEvents {
			c.Next()
			return
		}

		start := time.Now()
		requestID, _ := c.Get("request_id")
		if requestID == nil {
			requestID = generateID()
		}

		c.Next()

		// Log proxy event after request processing
		go m.logProxyEvent(c, requestID.(string), start)
	}
}

// logProxyEvent logs proxy-specific events
func (m *LoggingMiddleware) logProxyEvent(c *gin.Context, requestID string, startTime time.Time) {
	service := c.Param("service")

	// Get upstream URL from context
	upstreamURL := ""
	if url, exists := c.Get("upstream_url"); exists {
		if urlStr, ok := url.(string); ok {
			upstreamURL = urlStr
		}
	}

	// Get debug flag from context
	debug := false
	if debugFlag, exists := c.Get("debug_mode"); exists {
		if debugBool, ok := debugFlag.(bool); ok {
			debug = debugBool
		}
	}

	// Get error from context
	errorMsg := ""
	if err, exists := c.Get("proxy_error"); exists {
		if errStr, ok := err.(string); ok {
			errorMsg = errStr
		}
	}

	// Get request and response sizes
	requestSize := int64(0)
	if size, exists := c.Get("request_size"); exists {
		if sizeInt, ok := size.(int64); ok {
			requestSize = sizeInt
		}
	}

	responseSize := int64(0)
	if sizeHeader := c.Writer.Header().Get("Content-Length"); sizeHeader != "" {
		if size, err := strconv.ParseInt(sizeHeader, 10, 64); err == nil {
			responseSize = size
		}
	}

	// Create metadata
	metadata := make(map[string]string)
	if userAgent := c.Request.UserAgent(); userAgent != "" {
		metadata["user_agent"] = userAgent
	}
	if referer := c.Request.Referer(); referer != "" {
		metadata["referer"] = referer
	}

	entry := &models.ProxyLogEntry{
		ID:           requestID,
		Timestamp:    startTime,
		Service:      service,
		ClientIP:     c.ClientIP(),
		Method:       c.Request.Method,
		RequestURL:   c.Request.URL.String(),
		UpstreamURL:  upstreamURL,
		StatusCode:   c.Writer.Status(),
		Duration:     time.Since(startTime),
		RequestSize:  requestSize,
		ResponseSize: responseSize,
		Error:        errorMsg,
		Debug:        debug,
		Metadata:     metadata,
	}

	m.logger.LogProxyEvent(entry)
}
