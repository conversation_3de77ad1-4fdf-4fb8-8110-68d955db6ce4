package services

import (
	"fmt"
	"math/rand"
	"sync"
	"time"

	"nova-proxy/internal/models"
)

// Balancer manages load balancing across multiple upstreams
type Balancer struct {
	configs    map[string]*models.ServiceConfig
	mu         sync.RWMutex
	rng        *rand.Rand     // Local random generator
	roundRobin map[string]int // Round-robin counters per service
}

// NewBalancer creates a new balancer instance
func NewBalancer() *Balancer {
	return &Balancer{
		configs:    make(map[string]*models.ServiceConfig),
		rng:        rand.New(rand.NewSource(time.Now().UnixNano())),
		roundRobin: make(map[string]int),
	}
}

// LoadConfig loads configuration for a specific service
func (b *Balancer) LoadConfig(service string, config *models.ServiceConfig) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.configs[service] = config
}

// GetUpstream returns an upstream server using intelligent routing
// Priority: x-partner header > x-biz header > weighted round-robin
func (b *Balancer) GetUpstream(service string, partnerName string, businessCode string) (*models.Upstream, *models.Partner) {
	b.mu.RLock()
	defer b.mu.RUnlock()

	cfg, ok := b.configs[service]
	if !ok || len(cfg.Partners) == 0 {
		return nil, nil
	}

	// 1. Direct partner selection via x-partner header
	if partnerName != "" {
		if partner := b.getPartnerByName(cfg.Partners, partnerName); partner != nil {
			if upstream := b.selectUpstreamFromPartner(service, partner); upstream != nil {
				return upstream, partner
			}
		}
	}

	// 2. Business code routing via x-biz header
	if businessCode != "" {
		if partner := b.getPartnerByBusinessCode(cfg.Partners, businessCode); partner != nil {
			if upstream := b.selectUpstreamFromPartner(service, partner); upstream != nil {
				return upstream, partner
			}
		}
	}

	// 3. Weighted round-robin across all partners (default)
	return b.getUpstreamByWeightedRoundRobinAllPartners(service, cfg.Partners)
}

// GetUpstreamLegacy maintains backward compatibility for existing code
func (b *Balancer) GetUpstreamLegacy(service string) *models.Upstream {
	upstream, _ := b.GetUpstream(service, "", "")
	return upstream
}

// getPartnerByName finds partner by name
func (b *Balancer) getPartnerByName(partners []models.Partner, partnerName string) *models.Partner {
	for i := range partners {
		if partners[i].Name == partnerName {
			return &partners[i]
		}
	}
	return nil
}

// getPartnerByBusinessCode finds partner by business code
func (b *Balancer) getPartnerByBusinessCode(partners []models.Partner, businessCode string) *models.Partner {
	// Find partners that handle this business code
	var candidates []models.Partner
	for _, partner := range partners {
		for _, code := range partner.BusinessCodes {
			if code == businessCode {
				candidates = append(candidates, partner)
				break
			}
		}
	}

	if len(candidates) == 0 {
		return nil
	}

	// Use weighted selection among candidate partners
	return b.selectWeightedRandomPartner(candidates)
}

// selectUpstreamFromPartner selects an upstream from a specific partner
func (b *Balancer) selectUpstreamFromPartner(service string, partner *models.Partner) *models.Upstream {
	if len(partner.Upstreams) == 0 {
		return nil
	}

	if len(partner.Upstreams) == 1 {
		return &partner.Upstreams[0]
	}

	// Use weighted round-robin within the partner's upstreams
	partnerKey := fmt.Sprintf("%s-%s", service, partner.Name)
	return b.getUpstreamByWeightedRoundRobinFromList(partnerKey, partner.Upstreams)
}

// getUpstreamByWeightedRoundRobinAllPartners implements weighted round-robin across all partners
func (b *Balancer) getUpstreamByWeightedRoundRobinAllPartners(service string, partners []models.Partner) (*models.Upstream, *models.Partner) {
	if len(partners) == 0 {
		return nil, nil
	}

	// Collect all upstreams from all partners with their partner reference
	type upstreamWithPartner struct {
		upstream *models.Upstream
		partner  *models.Partner
		weight   int
	}

	var allUpstreams []upstreamWithPartner
	for i := range partners {
		for j := range partners[i].Upstreams {
			allUpstreams = append(allUpstreams, upstreamWithPartner{
				upstream: &partners[i].Upstreams[j],
				partner:  &partners[i],
				weight:   partners[i].Upstreams[j].Weight,
			})
		}
	}

	if len(allUpstreams) == 0 {
		return nil, nil
	}

	// Calculate total weight
	totalWeight := 0
	for _, u := range allUpstreams {
		totalWeight += u.weight
	}

	if totalWeight == 0 {
		// If no weights set, use simple round-robin
		counter := b.roundRobin[service]
		b.roundRobin[service] = (counter + 1) % len(allUpstreams)
		selected := allUpstreams[counter]
		return selected.upstream, selected.partner
	}

	// Weighted round-robin implementation
	counter := b.roundRobin[service]
	b.roundRobin[service] = (counter + 1) % totalWeight

	currentWeight := 0
	for _, u := range allUpstreams {
		currentWeight += u.weight
		if counter < currentWeight {
			return u.upstream, u.partner
		}
	}

	// Fallback
	selected := allUpstreams[0]
	return selected.upstream, selected.partner
}

// getUpstreamByWeightedRoundRobinFromList implements weighted round-robin for a specific list
func (b *Balancer) getUpstreamByWeightedRoundRobinFromList(key string, upstreams []models.Upstream) *models.Upstream {
	if len(upstreams) == 0 {
		return nil
	}

	// Calculate total weight
	totalWeight := 0
	for _, u := range upstreams {
		totalWeight += u.Weight
	}

	if totalWeight == 0 {
		// If no weights set, use simple round-robin
		counter := b.roundRobin[key]
		b.roundRobin[key] = (counter + 1) % len(upstreams)
		return &upstreams[counter]
	}

	// Weighted round-robin implementation
	counter := b.roundRobin[key]
	b.roundRobin[key] = (counter + 1) % totalWeight

	currentWeight := 0
	for i := range upstreams {
		currentWeight += upstreams[i].Weight
		if counter < currentWeight {
			return &upstreams[i]
		}
	}

	// Fallback
	return &upstreams[0]
}

// selectWeightedRandomPartner selects a partner using weighted random selection
func (b *Balancer) selectWeightedRandomPartner(partners []models.Partner) *models.Partner {
	if len(partners) == 0 {
		return nil
	}

	if len(partners) == 1 {
		return &partners[0]
	}

	// Calculate total weight based on sum of upstream weights
	totalWeight := 0
	for _, p := range partners {
		partnerWeight := 0
		for _, u := range p.Upstreams {
			partnerWeight += u.Weight
		}
		totalWeight += partnerWeight
	}

	if totalWeight == 0 {
		// If no weights, select randomly
		idx := b.rng.Intn(len(partners))
		return &partners[idx]
	}

	// Weighted random selection
	r := b.rng.Intn(totalWeight)
	currentWeight := 0

	for i := range partners {
		partnerWeight := 0
		for _, u := range partners[i].Upstreams {
			partnerWeight += u.Weight
		}
		currentWeight += partnerWeight
		if r < currentWeight {
			return &partners[i]
		}
	}

	// Fallback
	return &partners[0]
}

// GetServiceConfig returns the configuration for a specific service
func (b *Balancer) GetServiceConfig(service string) *models.ServiceConfig {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.configs[service]
}

// GetAllServices returns a list of all configured services
func (b *Balancer) GetAllServices() []string {
	b.mu.RLock()
	defer b.mu.RUnlock()

	services := make([]string, 0, len(b.configs))
	for service := range b.configs {
		services = append(services, service)
	}
	return services
}
