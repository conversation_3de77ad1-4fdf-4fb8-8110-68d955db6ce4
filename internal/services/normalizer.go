package services

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"nova-proxy/internal/models"
)

// ResponseNormalizer handles response normalization from different partners
type ResponseNormalizer struct{}

// NewResponseNormalizer creates a new response normalizer
func NewResponseNormalizer() *ResponseNormalizer {
	return &ResponseNormalizer{}
}

// NormalizeResponse converts partner response to standard format
func (rn *ResponseNormalizer) NormalizeResponse(
	partnerResponse []byte,
	upstream *models.Upstream,
	partner *models.Partner,
	service string,
	startTime time.Time,
	requestID string,
) (*models.StandardResponse, error) {

	// Parse partner response
	var partnerData map[string]interface{}
	if err := json.Unmarshal(partnerResponse, &partnerData); err != nil {
		return rn.createErrorResponse(partner.Name, "PARSE_ERROR",
			"Failed to parse partner response", time.Since(startTime), requestID), nil
	}

	// If no response mapping configured, return raw response
	if partner.ResponseMapping == nil {
		return rn.createRawResponse(partnerData, partner.Name,
			time.Since(startTime), requestID), nil
	}

	mapping := partner.ResponseMapping

	// Determine if response indicates success
	success := rn.isSuccessResponse(partnerData, mapping)

	if !success {
		return rn.createErrorFromPartnerResponse(partnerData, mapping,
			partner.Name, time.Since(startTime), requestID), nil
	}

	// Extract and normalize data based on service type
	normalizedData, err := rn.normalizeServiceData(partnerData, mapping, service)
	if err != nil {
		return rn.createErrorResponse(partner.Name, "NORMALIZATION_ERROR",
			err.Error(), time.Since(startTime), requestID), nil
	}

	// Create standard response
	return &models.StandardResponse{
		Success: true,
		Data:    normalizedData,
		Metadata: &models.ResponseMetadata{
			PartnerID:      partner.Name,
			ProcessingTime: float64(time.Since(startTime).Nanoseconds()) / 1e6,
			RequestID:      requestID,
		},
		Timestamp: time.Now(),
	}, nil
}

// isSuccessResponse determines if partner response indicates success
func (rn *ResponseNormalizer) isSuccessResponse(data map[string]interface{}, mapping *models.ResponseMapping) bool {
	if mapping.SuccessField == "" {
		return true // Assume success if no success field configured
	}

	successValue := rn.getNestedValue(data, mapping.SuccessField)
	if successValue == nil {
		return false
	}

	// Check against configured success values
	for _, expectedValue := range mapping.SuccessValues {
		if rn.valuesEqual(successValue, expectedValue) {
			return true
		}
	}

	return false
}

// normalizeServiceData normalizes data based on service type
func (rn *ResponseNormalizer) normalizeServiceData(
	data map[string]interface{},
	mapping *models.ResponseMapping,
	service string,
) (interface{}, error) {

	// Extract main data field if specified
	var serviceData map[string]interface{}
	if mapping.DataField != "" {
		if dataValue := rn.getNestedValue(data, mapping.DataField); dataValue != nil {
			if dataMap, ok := dataValue.(map[string]interface{}); ok {
				serviceData = dataMap
			} else {
				serviceData = data
			}
		} else {
			serviceData = data
		}
	} else {
		serviceData = data
	}

	switch service {
	case "ocr":
		return rn.normalizeOCRResponse(serviceData, mapping)
	case "liveness":
		return rn.normalizeLivenessResponse(serviceData, mapping)
	case "facematch":
		return rn.normalizeFaceMatchResponse(serviceData, mapping)
	default:
		return rn.normalizeGenericResponse(serviceData, mapping), nil
	}
}

// normalizeOCRResponse normalizes OCR service response
func (rn *ResponseNormalizer) normalizeOCRResponse(data map[string]interface{}, mapping *models.ResponseMapping) (*models.OCRResponse, error) {
	response := &models.OCRResponse{
		Fields: make(map[string]interface{}),
	}

	// Map confidence
	if mapping.ConfidenceField != "" {
		if conf := rn.getNestedValue(data, mapping.ConfidenceField); conf != nil {
			if confFloat, err := rn.toFloat64(conf); err == nil {
				response.Confidence = confFloat
			}
		}
	}

	// Map fields using field mappings
	for partnerField, standardField := range mapping.FieldMappings {
		if value := rn.getNestedValue(data, partnerField); value != nil {
			switch standardField {
			case "document_type":
				if str, ok := value.(string); ok {
					response.DocumentType = str
				}
			case "raw_text":
				if str, ok := value.(string); ok {
					response.RawText = str
				}
			default:
				response.Fields[standardField] = value
			}
		}
	}

	return response, nil
}

// normalizeLivenessResponse normalizes liveness detection response
func (rn *ResponseNormalizer) normalizeLivenessResponse(data map[string]interface{}, mapping *models.ResponseMapping) (*models.LivenessResponse, error) {
	response := &models.LivenessResponse{}

	// Map confidence and score
	if mapping.ConfidenceField != "" {
		if conf := rn.getNestedValue(data, mapping.ConfidenceField); conf != nil {
			if confFloat, err := rn.toFloat64(conf); err == nil {
				response.Confidence = confFloat
				response.Score = confFloat // Use confidence as score if no separate score field
			}
		}
	}

	// Map fields using field mappings
	for partnerField, standardField := range mapping.FieldMappings {
		if value := rn.getNestedValue(data, partnerField); value != nil {
			switch standardField {
			case "is_live":
				response.IsLive = rn.toBool(value)
			case "score":
				if scoreFloat, err := rn.toFloat64(value); err == nil {
					response.Score = scoreFloat
				}
			}
		}
	}

	return response, nil
}

// normalizeFaceMatchResponse normalizes face matching response
func (rn *ResponseNormalizer) normalizeFaceMatchResponse(data map[string]interface{}, mapping *models.ResponseMapping) (*models.FaceMatchResponse, error) {
	response := &models.FaceMatchResponse{}

	// Map confidence
	if mapping.ConfidenceField != "" {
		if conf := rn.getNestedValue(data, mapping.ConfidenceField); conf != nil {
			if confFloat, err := rn.toFloat64(conf); err == nil {
				response.Confidence = confFloat
				response.Score = confFloat
				response.Similarity = confFloat
			}
		}
	}

	// Map fields using field mappings
	for partnerField, standardField := range mapping.FieldMappings {
		if value := rn.getNestedValue(data, partnerField); value != nil {
			switch standardField {
			case "is_match":
				response.IsMatch = rn.toBool(value)
			case "score":
				if scoreFloat, err := rn.toFloat64(value); err == nil {
					response.Score = scoreFloat
				}
			case "similarity":
				if simFloat, err := rn.toFloat64(value); err == nil {
					response.Similarity = simFloat
				}
			}
		}
	}

	return response, nil
}

// normalizeGenericResponse handles generic field mapping
func (rn *ResponseNormalizer) normalizeGenericResponse(data map[string]interface{}, mapping *models.ResponseMapping) map[string]interface{} {
	result := make(map[string]interface{})

	for partnerField, standardField := range mapping.FieldMappings {
		if value := rn.getNestedValue(data, partnerField); value != nil {
			result[standardField] = value
		}
	}

	return result
}

// Helper functions
func (rn *ResponseNormalizer) getNestedValue(data map[string]interface{}, path string) interface{} {
	keys := strings.Split(path, ".")
	current := data

	for i, key := range keys {
		if i == len(keys)-1 {
			return current[key]
		}
		if next, ok := current[key].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}
	return nil
}

func (rn *ResponseNormalizer) valuesEqual(a, b interface{}) bool {
	return fmt.Sprint(a) == fmt.Sprint(b)
}

func (rn *ResponseNormalizer) toFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", value)
	}
}

func (rn *ResponseNormalizer) toBool(value interface{}) bool {
	switch v := value.(type) {
	case bool:
		return v
	case string:
		return strings.ToLower(v) == "true" || v == "1"
	case int:
		return v != 0
	case float64:
		return v != 0
	default:
		return false
	}
}

func (rn *ResponseNormalizer) createErrorResponse(partnerID, code, message string, duration time.Duration, requestID string) *models.StandardResponse {
	return &models.StandardResponse{
		Success: false,
		Error: &models.ErrorDetails{
			Code:    code,
			Message: message,
		},
		Metadata: &models.ResponseMetadata{
			PartnerID:      partnerID,
			ProcessingTime: float64(duration.Nanoseconds()) / 1e6,
			RequestID:      requestID,
		},
		Timestamp: time.Now(),
	}
}

func (rn *ResponseNormalizer) createErrorFromPartnerResponse(data map[string]interface{}, mapping *models.ResponseMapping, partnerID string, duration time.Duration, requestID string) *models.StandardResponse {
	errorCode := "PARTNER_ERROR"
	errorMessage := "Request failed"

	if mapping.ErrorCodeField != "" {
		if code := rn.getNestedValue(data, mapping.ErrorCodeField); code != nil {
			errorCode = fmt.Sprint(code)
		}
	}

	if mapping.MessageField != "" {
		if msg := rn.getNestedValue(data, mapping.MessageField); msg != nil {
			errorMessage = fmt.Sprint(msg)
		}
	}

	return rn.createErrorResponse(partnerID, errorCode, errorMessage, duration, requestID)
}

func (rn *ResponseNormalizer) createRawResponse(data map[string]interface{}, partnerID string, duration time.Duration, requestID string) *models.StandardResponse {
	return &models.StandardResponse{
		Success: true,
		Data:    data,
		Metadata: &models.ResponseMetadata{
			PartnerID:      partnerID,
			ProcessingTime: float64(duration.Nanoseconds()) / 1e6,
			RequestID:      requestID,
		},
		Timestamp: time.Now(),
	}
}
