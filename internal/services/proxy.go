package services

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"io"
	"mime/multipart"

	"nova-proxy/internal/models"
)

// ProxyService handles proxy-related operations
type ProxyService struct{}

// NewProxyService creates a new proxy service instance
func NewProxyService() *ProxyService {
	return &ProxyService{}
}

// BuildJSONFromReq converts mapped data to JSON format with base64 encoding for files
func (p *ProxyService) BuildJSONFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (interface{}, error) {
	jsonData := make(map[string]interface{})
	
	// Copy mapped data (stream convert files to base64)
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			f, err := fh.Open()
			if err != nil {
				return nil, err
			}
			defer f.Close()

			// Stream: Use bufio.Reader to read chunks, encode base64 on-the-fly
			reader := bufio.NewReader(f)
			var b bytes.Buffer
			encoder := base64.NewEncoder(base64.StdEncoding, &b)
			if _, err := io.Copy(encoder, reader); err != nil {
				return nil, err
			}
			encoder.Close() // Flush base64

			jsonData[key] = b.String() // Base64 string
		} else if str, ok := val.(string); ok {
			jsonData[key] = str
		}
	}
	
	// Add hardcoded fields
	for key, val := range hardcoded {
		jsonData[key] = val
	}
	
	return jsonData, nil
}

// BuildMultipartFromReq builds multipart form data from mapped data and hardcoded fields
func (p *ProxyService) BuildMultipartFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (io.Reader, string, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	
	// Add mapped files/text
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			fw, err := w.CreateFormFile(key, fh.Filename)
			if err != nil {
				return nil, "", err
			}
			f, err := fh.Open()
			if err != nil {
				return nil, "", err
			}
			defer f.Close()
			io.Copy(fw, f)
		} else if str, ok := val.(string); ok && str != "" {
			w.WriteField(key, str)
		}
	}
	
	// Add hardcoded as text fields
	for key, val := range hardcoded {
		w.WriteField(key, val)
	}
	
	w.Close()
	return &b, w.FormDataContentType(), nil
}

// PrepareRequestBody prepares the request body based on the upstream format
func (p *ProxyService) PrepareRequestBody(upstream *models.Upstream, mappedData map[string]interface{}) (io.Reader, string, error) {
	if upstream.Format == "json" {
		jsonData, err := p.BuildJSONFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			return nil, "", err
		}
		jsonBytes, _ := json.Marshal(jsonData)
		return bytes.NewReader(jsonBytes), "application/json", nil
	}
	
	// Default to multipart
	return p.BuildMultipartFromReq(mappedData, upstream.HardcodedFields)
}
