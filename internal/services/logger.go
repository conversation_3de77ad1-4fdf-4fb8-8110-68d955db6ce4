package services

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"nova-proxy/internal/models"
)

// AsyncLogger provides high-performance, non-blocking logging
type AsyncLogger struct {
	config          *models.LogConfig
	requestChan     chan *models.RequestLogEntry
	responseChan    chan *models.ResponseLogEntry
	proxyEventChan  chan *models.ProxyLogEntry
	genericLogChan  chan *models.LogEntry
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
	currentFile     *os.File
	currentFileSize int64
	fileIndex       int
	mu              sync.Mutex
}

// NewAsyncLogger creates a new async logger instance
func NewAsyncLogger(config *models.LogConfig) *AsyncLogger {
	ctx, cancel := context.WithCancel(context.Background())

	logger := &AsyncLogger{
		config:         config,
		requestChan:    make(chan *models.RequestLogEntry, config.BufferSize),
		responseChan:   make(chan *models.ResponseLogEntry, config.BufferSize),
		proxyEvent<PERSON>han: make(chan *models.ProxyLogEntry, config.BufferSize),
		genericLogChan: make(chan *models.LogEntry, config.BufferSize),
		ctx:            ctx,
		cancel:         cancel,
	}

	return logger
}

// Start begins the async logging process
func (l *AsyncLogger) Start() error {
	if !l.config.Enabled {
		return nil
	}

	// Ensure log directory exists
	if err := os.MkdirAll(l.config.Directory, 0755); err != nil {
		return fmt.Errorf("failed to create log directory: %w", err)
	}

	// Start worker goroutines
	l.wg.Add(4)
	go l.requestWorker()
	go l.responseWorker()
	go l.proxyEventWorker()
	go l.genericLogWorker()

	// Start periodic flush
	l.wg.Add(1)
	go l.flushWorker()

	return nil
}

// Stop gracefully shuts down the logger
func (l *AsyncLogger) Stop() {
	l.cancel()
	l.wg.Wait()

	l.mu.Lock()
	if l.currentFile != nil {
		l.currentFile.Close()
	}
	l.mu.Unlock()
}

// LogRequest logs a request entry (non-blocking)
func (l *AsyncLogger) LogRequest(entry *models.RequestLogEntry) {
	if !l.config.Enabled || !l.config.LogRequests {
		return
	}

	select {
	case l.requestChan <- entry:
	default:
		// Channel is full, drop the log entry to avoid blocking
		// In production, you might want to increment a dropped logs counter
	}
}

// LogResponse logs a response entry (non-blocking)
func (l *AsyncLogger) LogResponse(entry *models.ResponseLogEntry) {
	if !l.config.Enabled || !l.config.LogResponses {
		return
	}

	select {
	case l.responseChan <- entry:
	default:
		// Channel is full, drop the log entry
	}
}

// LogProxyEvent logs a proxy event (non-blocking)
func (l *AsyncLogger) LogProxyEvent(entry *models.ProxyLogEntry) {
	if !l.config.Enabled || !l.config.LogProxyEvents {
		return
	}

	select {
	case l.proxyEventChan <- entry:
	default:
		// Channel is full, drop the log entry
	}
}

// LogGeneric logs a generic log entry (non-blocking)
func (l *AsyncLogger) LogGeneric(entry *models.LogEntry) {
	if !l.config.Enabled {
		return
	}

	select {
	case l.genericLogChan <- entry:
	default:
		// Channel is full, drop the log entry
	}
}

// requestWorker processes request log entries
func (l *AsyncLogger) requestWorker() {
	defer l.wg.Done()

	buffer := make([]*models.RequestLogEntry, 0, 100)
	ticker := time.NewTicker(l.config.FlushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			// Flush remaining entries before exit
			if len(buffer) > 0 {
				l.writeRequestEntries(buffer)
			}
			return
		case entry := <-l.requestChan:
			buffer = append(buffer, entry)
			if len(buffer) >= 100 {
				l.writeRequestEntries(buffer)
				buffer = buffer[:0]
			}
		case <-ticker.C:
			if len(buffer) > 0 {
				l.writeRequestEntries(buffer)
				buffer = buffer[:0]
			}
		}
	}
}

// responseWorker processes response log entries
func (l *AsyncLogger) responseWorker() {
	defer l.wg.Done()

	buffer := make([]*models.ResponseLogEntry, 0, 100)
	ticker := time.NewTicker(l.config.FlushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			if len(buffer) > 0 {
				l.writeResponseEntries(buffer)
			}
			return
		case entry := <-l.responseChan:
			buffer = append(buffer, entry)
			if len(buffer) >= 100 {
				l.writeResponseEntries(buffer)
				buffer = buffer[:0]
			}
		case <-ticker.C:
			if len(buffer) > 0 {
				l.writeResponseEntries(buffer)
				buffer = buffer[:0]
			}
		}
	}
}

// proxyEventWorker processes proxy event log entries
func (l *AsyncLogger) proxyEventWorker() {
	defer l.wg.Done()

	buffer := make([]*models.ProxyLogEntry, 0, 100)
	ticker := time.NewTicker(l.config.FlushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			if len(buffer) > 0 {
				l.writeProxyEventEntries(buffer)
			}
			return
		case entry := <-l.proxyEventChan:
			buffer = append(buffer, entry)
			if len(buffer) >= 100 {
				l.writeProxyEventEntries(buffer)
				buffer = buffer[:0]
			}
		case <-ticker.C:
			if len(buffer) > 0 {
				l.writeProxyEventEntries(buffer)
				buffer = buffer[:0]
			}
		}
	}
}

// genericLogWorker processes generic log entries
func (l *AsyncLogger) genericLogWorker() {
	defer l.wg.Done()

	buffer := make([]*models.LogEntry, 0, 100)
	ticker := time.NewTicker(l.config.FlushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			if len(buffer) > 0 {
				l.writeGenericEntries(buffer)
			}
			return
		case entry := <-l.genericLogChan:
			buffer = append(buffer, entry)
			if len(buffer) >= 100 {
				l.writeGenericEntries(buffer)
				buffer = buffer[:0]
			}
		case <-ticker.C:
			if len(buffer) > 0 {
				l.writeGenericEntries(buffer)
				buffer = buffer[:0]
			}
		}
	}
}

// flushWorker periodically flushes the current file
func (l *AsyncLogger) flushWorker() {
	defer l.wg.Done()

	ticker := time.NewTicker(l.config.FlushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			l.mu.Lock()
			if l.currentFile != nil {
				l.currentFile.Sync()
			}
			l.mu.Unlock()
		}
	}
}

// writeRequestEntries writes request entries to file
func (l *AsyncLogger) writeRequestEntries(entries []*models.RequestLogEntry) {
	for _, entry := range entries {
		data, err := json.Marshal(entry)
		if err != nil {
			continue
		}
		l.writeToFile("requests", data)
	}
}

// writeResponseEntries writes response entries to file
func (l *AsyncLogger) writeResponseEntries(entries []*models.ResponseLogEntry) {
	for _, entry := range entries {
		data, err := json.Marshal(entry)
		if err != nil {
			continue
		}
		l.writeToFile("responses", data)
	}
}

// writeProxyEventEntries writes proxy event entries to file
func (l *AsyncLogger) writeProxyEventEntries(entries []*models.ProxyLogEntry) {
	for _, entry := range entries {
		data, err := json.Marshal(entry)
		if err != nil {
			continue
		}
		l.writeToFile("proxy_events", data)
	}
}

// writeGenericEntries writes generic log entries to file
func (l *AsyncLogger) writeGenericEntries(entries []*models.LogEntry) {
	for _, entry := range entries {
		data, err := json.Marshal(entry)
		if err != nil {
			continue
		}
		l.writeToFile("general", data)
	}
}

// writeToFile writes data to the appropriate log file with rotation
func (l *AsyncLogger) writeToFile(logType string, data []byte) {
	l.mu.Lock()
	defer l.mu.Unlock()

	// Check if we need to rotate the file
	if l.currentFile == nil || l.currentFileSize+int64(len(data)) > l.config.MaxFileSize {
		if err := l.rotateFile(logType); err != nil {
			return
		}
	}

	// Write the data
	data = append(data, '\n')
	if n, err := l.currentFile.Write(data); err == nil {
		l.currentFileSize += int64(n)
	}
}

// rotateFile creates a new log file and closes the old one
func (l *AsyncLogger) rotateFile(logType string) error {
	// Close current file if open
	if l.currentFile != nil {
		l.currentFile.Close()
	}

	// Generate new filename
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	filename := fmt.Sprintf("%s_%s_%d.log", logType, timestamp, l.fileIndex)
	filepath := filepath.Join(l.config.Directory, filename)

	// Create new file
	file, err := os.OpenFile(filepath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}

	l.currentFile = file
	l.currentFileSize = 0
	l.fileIndex++

	// Clean up old files if necessary
	go l.cleanupOldFiles(logType)

	return nil
}

// cleanupOldFiles removes old log files based on configuration
func (l *AsyncLogger) cleanupOldFiles(logType string) {
	if l.config.MaxFiles <= 0 {
		return
	}

	pattern := filepath.Join(l.config.Directory, fmt.Sprintf("%s_*.log", logType))
	matches, err := filepath.Glob(pattern)
	if err != nil {
		return
	}

	if len(matches) > l.config.MaxFiles {
		// Sort by modification time and remove oldest files
		// This is a simplified implementation
		for i := 0; i < len(matches)-l.config.MaxFiles; i++ {
			os.Remove(matches[i])
		}
	}
}
