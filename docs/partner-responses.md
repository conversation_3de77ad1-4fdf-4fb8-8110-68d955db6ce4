# Partner Response Examples

This document shows how different partner responses are normalized into a consistent format.

## OCR Service Examples

### Partner A Response (Banking)
```json
{
  "status": "success",
  "result": {
    "confidence": 0.95,
    "document_type": "national_id",
    "extracted_text": "JOHN DOE\n*********\nDOB: 01/01/1990",
    "fields": {
      "name": "<PERSON>",
      "id_number": "*********",
      "date_of_birth": "1990-01-01"
    }
  }
}
```

### Partner B Response (Insurance)
```json
{
  "success": true,
  "confidence_score": 0.92,
  "doc_type": "id_card",
  "text_content": "JOHN DOE\n*********\n01/01/1990",
  "extracted_data": {
    "full_name": "<PERSON>",
    "identification": "*********",
    "birth_date": "1990-01-01"
  }
}
```

### Partner C Response (Retail)
```json
{
  "code": 0,
  "message": "Processing completed successfully",
  "payload": {
    "accuracy": 0.88,
    "type": "identity_card",
    "content": "JOHN DOE\n*********\nBorn: 01/01/1990",
    "person": {
      "name": "<PERSON>",
      "id": "*********",
      "dob": "1990-01-01"
    }
  }
}
```

### Normalized Response (All Partners)
```json
{
  "success": true,
  "data": {
    "document_type": "id_card",
    "confidence": 0.95,
    "fields": {
      "name": "John Doe",
      "id_number": "*********",
      "date_of_birth": "1990-01-01"
    },
    "raw_text": "JOHN DOE\n*********\nDOB: 01/01/1990"
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time_ms": 1250.5,
    "request_id": "ocr-1694*********"
  },
  "timestamp": "2025-09-12T10:30:00.123Z"
}
```

## Liveness Service Examples

### Partner A Response
```json
{
  "status": "success",
  "result": {
    "is_alive": true,
    "confidence": 0.94,
    "liveness_score": 0.91
  }
}
```

### Partner D Response
```json
{
  "success": true,
  "live": true,
  "confidence": 0.89,
  "score": 0.87,
  "details": {
    "blink_detected": true,
    "movement_detected": true
  }
}
```

### Normalized Response
```json
{
  "success": true,
  "data": {
    "is_live": true,
    "confidence": 0.94,
    "score": 0.91,
    "checks": []
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time_ms": 890.2,
    "request_id": "liveness-1694123456790"
  },
  "timestamp": "2025-09-12T10:30:01.456Z"
}
```

## Face Match Service Examples

### Partner B Response
```json
{
  "status": "ok",
  "match_result": {
    "is_match": true,
    "confidence": 0.89,
    "similarity_score": 0.91,
    "match_score": 0.89
  }
}
```

### Normalized Response
```json
{
  "success": true,
  "data": {
    "is_match": true,
    "confidence": 0.89,
    "score": 0.89,
    "similarity": 0.91
  },
  "metadata": {
    "partner_id": "partner-b",
    "processing_time_ms": 1100.8,
    "request_id": "facematch-1694123456791"
  },
  "timestamp": "2025-09-12T10:30:02.789Z"
}
```

## Error Response Examples

### Partner Error Response
```json
{
  "status": "error",
  "error": {
    "code": "INVALID_DOCUMENT",
    "message": "Document quality too low for processing"
  }
}
```

### Normalized Error Response
```json
{
  "success": false,
  "error": {
    "code": "INVALID_DOCUMENT",
    "message": "Document quality too low for processing"
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time_ms": 450.3,
    "request_id": "ocr-*************"
  },
  "timestamp": "2025-09-12T10:30:03.012Z"
}
```

## Testing Commands

### Test OCR with Different Partners

```bash
# Partner A (banking)
curl -X POST http://localhost:8080/ocr \
  -H "x-biz: banking" \
  -F "img1=@id_front.jpg" \
  -F "img2=@id_back.jpg"

# Partner B (insurance)  
curl -X POST http://localhost:8080/ocr \
  -H "x-biz: insurance" \
  -F "img1=@id_front.jpg" \
  -F "img2=@id_back.jpg"

# Specific partner
curl -X POST http://localhost:8080/ocr \
  -H "x-partner: partner-c" \
  -F "img1=@id_front.jpg" \
  -F "img2=@id_back.jpg"
```

All commands above will return the same normalized response format, regardless of which partner processes the request.
