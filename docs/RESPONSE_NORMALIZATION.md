# Response Normalization Documentation

Nova-proxy provides automatic response normalization to ensure consistent API responses regardless of which partner processes the request.

## Overview

Different partners often return responses in varying formats and structures. Response normalization transforms these diverse formats into a standardized schema that clients can rely on.

## Configuration

### Response Mapping Fields

Configure response normalization in the upstream configuration:

```json
{
  "response_mapping": {
    "success_field": "status",
    "success_values": ["success", "ok", 200],
    "data_field": "result",
    "error_field": "error",
    "error_code_field": "error.code",
    "message_field": "error.message",
    "confidence_field": "result.confidence",
    "field_mappings": {
      "partner_field_path": "standard_field_name"
    }
  }
}
```

#### Configuration Fields:

- **`success_field`**: Path to field indicating success/failure
- **`success_values`**: Array of values that indicate success
- **`data_field`**: Path to main data payload
- **`error_field`**: Path to error information
- **`error_code_field`**: Path to error code
- **`message_field`**: Path to error/success message
- **`confidence_field`**: Path to confidence score
- **`field_mappings`**: Map partner field paths to standard field names

### Field Path Syntax

Use dot notation for nested fields:
- `"result.confidence"` → `data.result.confidence`
- `"error.details.code"` → `data.error.details.code`

## Partner Response Examples

### Partner A (Banking)
```json
{
  "status": "success",
  "result": {
    "confidence": 0.95,
    "document_type": "id_card",
    "extracted_text": "JOHN DOE...",
    "fields": {
      "name": "John Doe",
      "id_number": "*********"
    }
  }
}
```

### Partner B (Insurance)
```json
{
  "success": true,
  "confidence_score": 0.92,
  "doc_type": "passport",
  "text_content": "JOHN DOE...",
  "extracted_data": {
    "full_name": "John Doe",
    "identification": "P*********"
  }
}
```

### Partner C (Retail)
```json
{
  "code": 0,
  "message": "Processing completed",
  "payload": {
    "accuracy": 0.88,
    "type": "driver_license",
    "content": "JOHN DOE...",
    "person": {
      "name": "John Doe",
      "id": "DL*********"
    }
  }
}
```

## Normalized Output

All three partners above would produce this standardized response:

```json
{
  "success": true,
  "data": {
    "document_type": "id_card",
    "confidence": 0.95,
    "fields": {
      "name": "John Doe",
      "id_number": "*********"
    },
    "raw_text": "JOHN DOE..."
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time_ms": 1250.5,
    "request_id": "ocr-*********0"
  },
  "timestamp": "2025-09-12T10:30:00Z"
}
```

## Service-Specific Normalization

### OCR Service

Standard fields:
- `document_type`: Type of document processed
- `confidence`: Overall confidence score (0-1)
- `fields`: Extracted field data
- `raw_text`: Raw extracted text

### Liveness Service

Standard fields:
- `is_live`: Boolean indicating if person is live
- `confidence`: Overall confidence score (0-1)
- `score`: Liveness score (0-1)
- `checks`: Array of individual check results

### Face Match Service

Standard fields:
- `is_match`: Boolean indicating if faces match
- `confidence`: Overall confidence score (0-1)
- `score`: Match score (0-1)
- `similarity`: Similarity percentage (0-1)

## Error Handling

### Partner Error Response
```json
{
  "status": "error",
  "error": {
    "code": "INVALID_DOCUMENT",
    "message": "Document format not supported"
  }
}
```

### Normalized Error Response
```json
{
  "success": false,
  "error": {
    "code": "INVALID_DOCUMENT",
    "message": "Document format not supported"
  },
  "metadata": {
    "partner_id": "partner-a",
    "processing_time_ms": 500.2,
    "request_id": "ocr-*********0"
  },
  "timestamp": "2025-09-12T10:30:00Z"
}
```

## Configuration Examples

### Simple Mapping
```json
{
  "response_mapping": {
    "success_field": "success",
    "success_values": [true],
    "confidence_field": "confidence",
    "field_mappings": {
      "result": "is_live",
      "score": "score"
    }
  }
}
```

### Complex Nested Mapping
```json
{
  "response_mapping": {
    "success_field": "response.status",
    "success_values": ["OK", "SUCCESS"],
    "data_field": "response.data",
    "error_code_field": "response.error.errorCode",
    "message_field": "response.error.errorMessage",
    "confidence_field": "response.data.confidence",
    "field_mappings": {
      "response.data.extracted.fullName": "name",
      "response.data.extracted.documentNumber": "id_number",
      "response.data.extracted.dateOfBirth": "date_of_birth",
      "response.data.documentType": "document_type"
    }
  }
}
```

## Benefits

1. **Consistent API**: Clients receive uniform responses regardless of partner
2. **Easy Integration**: Single response format to handle
3. **Partner Flexibility**: Add new partners without changing client code
4. **Error Standardization**: Consistent error handling across partners
5. **Metadata Tracking**: Built-in performance and routing information

## Fallback Behavior

If no `response_mapping` is configured, the raw partner response is returned unchanged, maintaining backward compatibility.
