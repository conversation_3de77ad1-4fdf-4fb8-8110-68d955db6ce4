package main

import (
	"math/rand"
	"sync"
	"time"
)

type Upstream struct {
	URL             string            `json:"url"`
	Format          string            `json:"format"`
	Weight          int               `json:"weight"`
	FieldMapping    map[string]string `json:"field_mapping,omitempty"`
	HardcodedFields map[string]string `json:"hardcoded_fields,omitempty"`
	Debug           bool              `json:"debug,omitempty"`
}

type ServiceConfig struct {
	Upstreams []Upstream `json:"upstreams"`
}

type Balancer struct {
	configs map[string]*ServiceConfig
	mu      sync.RWMutex
	current map[string]int // Index hiện tại cho từng service
	rng     *rand.Rand     // Local random generator
}

func NewBalancer() *Balancer {
	return &Balancer{
		configs: make(map[string]*ServiceConfig),
		current: make(map[string]int),
		rng:     rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

func (b *Balancer) LoadConfig(service string, config *ServiceConfig) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.configs[service] = config
	b.current[service] = 0 // Reset index
}

func (b *Balancer) GetUpstream(service string) *Upstream {
	b.mu.RLock()
	defer b.mu.RUnlock()
	cfg, ok := b.configs[service]
	if !ok || len(cfg.Upstreams) == 0 {
		return nil
	}

	// Tính total weight
	totalWeight := 0
	for _, u := range cfg.Upstreams {
		totalWeight += u.Weight
	}

	// Chọn weighted random (đơn giản, có thể optimize thành cycle)
	r := b.rng.Intn(totalWeight)
	currentWeight := 0
	for _, u := range cfg.Upstreams {
		currentWeight += u.Weight
		if r < currentWeight {
			return &u
		}
	}

	// Fallback
	return &cfg.Upstreams[0]
}
