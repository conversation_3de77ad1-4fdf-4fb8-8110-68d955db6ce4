package main

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httputil"
	"net/url"
	"reflect"

	"github.com/gin-gonic/gin"
)

type Proxy struct {
	balancer *Balancer
}

func NewProxy(b *Balancer) *Proxy {
	return &Proxy{balancer: b}
}

// OcrRequest: Struct cho ocr với binding tags
type OcrRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
}

// LivenessRequest
type LivenessRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
	Img3 *multipart.FileHeader `form:"img3" binding:"required"`
}

// FacematchRequest
type FacematchRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
}

// buildDebugResponse: Tạo debug info
func buildDebugResponse(c *gin.Context, service string, reqStruct interface{}, upstream *Upstream, mappedData map[string]interface{}, contentType string, body io.Reader) gin.H {
	// Original fields (sử dụng reflect để extract)
	originalFields := make(map[string]interface{})
	v := reflect.ValueOf(reqStruct).Elem()
	for i := 0; i < v.NumField(); i++ {
		field := v.Type().Field(i)
		value := v.Field(i)
		formTag := field.Tag.Get("form")
		if formTag == "" {
			continue
		}
		switch value.Kind() {
		case reflect.Ptr: // FileHeader
			if fh, ok := value.Interface().(*multipart.FileHeader); ok && fh != nil {
				originalFields[formTag] = fh.Filename // Chỉ name, không full content
			}
		case reflect.String:
			if str := value.String(); str != "" {
				originalFields[formTag] = str
			}
		}
	}

	// Built body sample
	var bodySample string
	if body != nil {
		if r, ok := body.(*bytes.Buffer); ok {
			bodySample = r.String() // Full nếu nhỏ, hoặc truncate
		} else if r, ok := body.(*bytes.Reader); ok {
			// Cho JSON, read sample
			buf := new(bytes.Buffer)
			tee := io.TeeReader(r, buf)
			limited := io.LimitReader(tee, 500) // Truncate nếu lớn
			io.Copy(io.Discard, limited)
			bodySample = buf.String()
			if len(bodySample) > 500 {
				bodySample = bodySample[:497] + "... (truncated)"
			}
		} else {
			bodySample = fmt.Sprintf("Body type: %T (sample not readable)", body)
		}
	}

	// Multipart fields list (nếu format multipart)
	var multipartFields []string
	if upstream.Format == "multipart/form-data" {
		for key := range mappedData {
			multipartFields = append(multipartFields, key)
		}
		for key := range upstream.HardcodedFields {
			multipartFields = append(multipartFields, key)
		}
	}

	return gin.H{
		"debug_mode": true,
		"service":    service,
		"selected_upstream": gin.H{
			"url":    upstream.URL,
			"format": upstream.Format,
			"weight": upstream.Weight,
		},
		"original_request_fields": originalFields,
		"mapped_fields":           mappedData, // Với file: chỉ names
		"hardcoded_fields":        upstream.HardcodedFields,
		"built_body_sample":       bodySample,
		"content_type":            contentType,
		"multipart_fields_list":   multipartFields,  // Nếu multipart
		"proxy_url":               upstream.URL,     // Nơi sẽ đi nếu không debug
		"copied_headers":          c.Request.Header, // Full headers từ client
	}
}

// getRequestStruct: Trả về pointer đến struct tương ứng
func getRequestStruct(service string) interface{} {
	switch service {
	case "ocr":
		return &OcrRequest{}
	case "liveness":
		return &LivenessRequest{}
	case "facematch":
		return &FacematchRequest{}
	default:
		return nil
	}
}

// applyMappingToReq: Áp dụng mapping cho request struct (sử dụng reflect để rename keys logic)
func applyMappingToReq(req interface{}, mapping map[string]string) map[string]interface{} {
	v := reflect.ValueOf(req).Elem()
	data := make(map[string]interface{})
	for i := 0; i < v.NumField(); i++ {
		field := v.Type().Field(i)
		value := v.Field(i)
		formTag := field.Tag.Get("form")
		if formTag == "" {
			continue
		}
		newKey := mapping[formTag]
		if newKey == "" {
			newKey = formTag
		}
		switch value.Kind() {
		case reflect.Ptr: // FileHeader
			if fh, ok := value.Interface().(*multipart.FileHeader); ok && fh != nil {
				data[newKey] = fh
			}
		case reflect.String:
			if str := value.String(); str != "" {
				data[newKey] = str
			}
		}
	}
	return data
}

// buildJSONFromReq: Cập nhật với stream
func buildJSONFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (interface{}, error) {
	jsonData := make(map[string]interface{})
	// Copy mapped data (stream convert files to base64)
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			f, err := fh.Open()
			if err != nil {
				return nil, err
			}
			defer f.Close()

			// Stream: Sử dụng bufio.Reader để đọc chunk, encode base64 on-the-fly
			reader := bufio.NewReader(f)
			var b bytes.Buffer
			encoder := base64.NewEncoder(base64.StdEncoding, &b)
			if _, err := io.Copy(encoder, reader); err != nil {
				return nil, err
			}
			encoder.Close() // Flush base64

			jsonData[key] = b.String() // Base64 string
		} else if str, ok := val.(string); ok {
			jsonData[key] = str
		}
	}
	// Add hardcoded fields (giữ nguyên)
	for key, val := range hardcoded {
		jsonData[key] = val
	}
	return jsonData, nil
}

// buildMultipartFromReq: Build multipart từ mapped data + hardcoded
func buildMultipartFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (io.Reader, string, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	// Add mapped files/text
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			fw, err := w.CreateFormFile(key, fh.Filename)
			if err != nil {
				return nil, "", err
			}
			f, err := fh.Open()
			if err != nil {
				return nil, "", err
			}
			defer f.Close()
			io.Copy(fw, f)
		} else if str, ok := val.(string); ok && str != "" {
			w.WriteField(key, str)
		}
	}
	// Add hardcoded as text fields
	for key, val := range hardcoded {
		w.WriteField(key, val)
	}
	w.Close()
	return &b, w.FormDataContentType(), nil
}

// ProxyHandler: Cập nhật với debug check
func (p *Proxy) ProxyHandler(c *gin.Context) {
	service := c.Param("service")
	upstream := p.balancer.GetUpstream(service)
	if upstream == nil {
		c.JSON(404, gin.H{"error": "No upstream found"})
		return
	}

	// Bind và validate (giữ nguyên)
	reqStruct := getRequestStruct(service)
	if reqStruct == nil {
		c.JSON(400, gin.H{"error": "Invalid service"})
		return
	}
	if err := c.ShouldBind(reqStruct); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Áp dụng mapping
	mappedData := applyMappingToReq(reqStruct, upstream.FieldMapping)

	var body io.Reader
	var contentType string
	if upstream.Debug {
		// Build body như bình thường để sample
		if upstream.Format == "json" {
			jsonData, err := buildJSONFromReq(mappedData, upstream.HardcodedFields)
			if err != nil {
				c.JSON(400, gin.H{"error": err.Error()})
				return
			}
			jsonBytes, _ := json.Marshal(jsonData)
			body = bytes.NewReader(jsonBytes)
			contentType = "application/json"
		} else {
			var err error
			body, contentType, err = buildMultipartFromReq(mappedData, upstream.HardcodedFields)
			if err != nil {
				c.JSON(400, gin.H{"error": err.Error()})
				return
			}
		}

		// Return debug response
		debugResp := buildDebugResponse(c, service, reqStruct, upstream, mappedData, contentType, body)
		c.JSON(200, debugResp)
		return
	}

	if upstream.Format == "json" {
		jsonData, err := buildJSONFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		jsonBytes, _ := json.Marshal(jsonData)
		body = bytes.NewReader(jsonBytes)
		contentType = "application/json"
	} else {
		var err error
		body, contentType, err = buildMultipartFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
	}

	// Tạo req và proxy (giữ nguyên logic cũ)
	u, _ := url.Parse(upstream.URL)
	req, _ := http.NewRequest(c.Request.Method, upstream.URL, body)
	req.Header.Set("Content-Type", contentType)
	for key, values := range c.Request.Header {
		if key != "Content-Length" {
			for _, value := range values {
				req.Header.Add(key, value)
			}
		}
	}

	proxy := httputil.NewSingleHostReverseProxy(u)
	proxy.Transport = &http.Transport{}
	originalDirector := proxy.Director
	proxy.Director = func(r *http.Request) {
		originalDirector(r)
		*r = *req
		r.URL = u
	}
	proxy.ServeHTTP(c.Writer, req)
}
