package main

import (
	"bufio"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httputil"
	"net/url"
	"reflect"
	"strings"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
)

type Proxy struct {
	balancer *Balancer
}

func NewProxy(b *Balancer) *Proxy {
	return &Proxy{balancer: b}
}

// OcrRequest: Struct cho ocr với binding tags
type OcrRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
}

// LivenessRequest
type LivenessRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
	Img3 *multipart.FileHeader `form:"img3" binding:"required"`
}

// FacematchRequest
type FacematchRequest struct {
	Img1 *multipart.FileHeader `form:"img1" binding:"required"`
	Img2 *multipart.FileHeader `form:"img2" binding:"required"`
}

// isBinaryContent checks if the content appears to be binary
func isBinaryContent(data []byte) bool {
	// Check for null bytes or non-UTF8 content
	if bytes.Contains(data, []byte{0}) {
		return true
	}

	// Check if content is valid UTF-8
	if !utf8.Valid(data) {
		return true
	}

	// Check for common binary file signatures
	if len(data) >= 4 {
		// Check for common binary file headers
		header := data[:4]
		binarySignatures := [][]byte{
			{0xFF, 0xD8, 0xFF},       // JPEG
			{0x89, 0x50, 0x4E, 0x47}, // PNG
			{0x47, 0x49, 0x46},       // GIF
			{0x25, 0x50, 0x44, 0x46}, // PDF
			{0x50, 0x4B, 0x03, 0x04}, // ZIP
		}

		for _, sig := range binarySignatures {
			if bytes.HasPrefix(header, sig) {
				return true
			}
		}
	}

	return false
}

// formatFileContent returns a formatted string for file content in debug mode
func formatFileContent(fh *multipart.FileHeader) string {
	if fh == nil {
		return "nil"
	}

	// Try to read a small sample to check if it's binary
	f, err := fh.Open()
	if err != nil {
		return fmt.Sprintf("FILE_CONTENT:%d bytes (error reading: %v)", fh.Size, err)
	}
	defer f.Close()

	// Read first 512 bytes to check content type
	sample := make([]byte, 512)
	n, _ := f.Read(sample)
	sample = sample[:n]

	if isBinaryContent(sample) {
		return fmt.Sprintf("FILE_CONTENT:%d bytes", fh.Size)
	}

	// If it's text content and small enough, show a preview
	if fh.Size <= 1024 {
		f.Seek(0, 0) // Reset to beginning
		content, err := io.ReadAll(f)
		if err != nil {
			return fmt.Sprintf("FILE_CONTENT:%d bytes (error reading: %v)", fh.Size, err)
		}
		return fmt.Sprintf("TEXT_CONTENT:%d bytes: %s", fh.Size, strings.TrimSpace(string(content)))
	}

	return fmt.Sprintf("TEXT_CONTENT:%d bytes (too large to preview)", fh.Size)
}

// buildDebugResponse: Tạo debug info
func buildDebugResponse(c *gin.Context, service string, reqStruct interface{}, upstream *Upstream, mappedData map[string]interface{}, contentType string, body io.Reader) gin.H {
	// Original fields (sử dụng reflect để extract)
	originalFields := make(map[string]interface{})
	v := reflect.ValueOf(reqStruct).Elem()
	for i := 0; i < v.NumField(); i++ {
		field := v.Type().Field(i)
		value := v.Field(i)
		formTag := field.Tag.Get("form")
		if formTag == "" {
			continue
		}
		switch value.Kind() {
		case reflect.Ptr: // FileHeader
			if fh, ok := value.Interface().(*multipart.FileHeader); ok && fh != nil {
				originalFields[formTag] = formatFileContent(fh)
			}
		case reflect.String:
			if str := value.String(); str != "" {
				originalFields[formTag] = str
			}
		}
	}

	// Built body sample
	var bodySample string
	if body != nil {
		if r, ok := body.(*bytes.Buffer); ok {
			data := r.Bytes()
			if isBinaryContent(data) {
				bodySample = fmt.Sprintf("BINARY_CONTENT:%d bytes", len(data))
			} else {
				content := r.String()
				if len(content) > 500 {
					bodySample = content[:497] + "... (truncated)"
				} else {
					bodySample = content
				}
			}
		} else if r, ok := body.(*bytes.Reader); ok {
			// Cho JSON, read sample
			buf := new(bytes.Buffer)
			tee := io.TeeReader(r, buf)
			limited := io.LimitReader(tee, 500) // Truncate nếu lớn
			io.Copy(io.Discard, limited)
			data := buf.Bytes()
			if isBinaryContent(data) {
				bodySample = fmt.Sprintf("BINARY_CONTENT:%d+ bytes", len(data))
			} else {
				bodySample = buf.String()
				if len(bodySample) > 500 {
					bodySample = bodySample[:497] + "... (truncated)"
				}
			}
		} else {
			bodySample = fmt.Sprintf("Body type: %T (sample not readable)", body)
		}
	}

	// Multipart fields list (nếu format multipart)
	var multipartFields []string
	if upstream.Format == "multipart/form-data" {
		for key := range mappedData {
			multipartFields = append(multipartFields, key)
		}
		for key := range upstream.HardcodedFields {
			multipartFields = append(multipartFields, key)
		}
	}

	// Format mapped fields for debug display
	debugMappedFields := make(map[string]interface{})
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			debugMappedFields[key] = formatFileContent(fh)
		} else {
			debugMappedFields[key] = val
		}
	}

	return gin.H{
		"debug_mode": true,
		"service":    service,
		"selected_upstream": gin.H{
			"url":    upstream.URL,
			"format": upstream.Format,
			"weight": upstream.Weight,
		},
		"original_request_fields": originalFields,
		"mapped_fields":           debugMappedFields, // Với file: formatted content info
		"hardcoded_fields":        upstream.HardcodedFields,
		"built_body_sample":       bodySample,
		"content_type":            contentType,
		"multipart_fields_list":   multipartFields,  // Nếu multipart
		"proxy_url":               upstream.URL,     // Nơi sẽ đi nếu không debug
		"copied_headers":          c.Request.Header, // Full headers từ client
	}
}

// getRequestStruct: Trả về pointer đến struct tương ứng
func getRequestStruct(service string) interface{} {
	switch service {
	case "ocr":
		return &OcrRequest{}
	case "liveness":
		return &LivenessRequest{}
	case "facematch":
		return &FacematchRequest{}
	default:
		return nil
	}
}

// applyMappingToReq: Áp dụng mapping cho request struct (sử dụng reflect để rename keys logic)
func applyMappingToReq(req interface{}, mapping map[string]string) map[string]interface{} {
	v := reflect.ValueOf(req).Elem()
	data := make(map[string]interface{})
	for i := 0; i < v.NumField(); i++ {
		field := v.Type().Field(i)
		value := v.Field(i)
		formTag := field.Tag.Get("form")
		if formTag == "" {
			continue
		}
		newKey := mapping[formTag]
		if newKey == "" {
			newKey = formTag
		}
		switch value.Kind() {
		case reflect.Ptr: // FileHeader
			if fh, ok := value.Interface().(*multipart.FileHeader); ok && fh != nil {
				data[newKey] = fh
			}
		case reflect.String:
			if str := value.String(); str != "" {
				data[newKey] = str
			}
		}
	}
	return data
}

// buildJSONFromReqDebug: Build JSON for debug mode with file content info
func buildJSONFromReqDebug(mappedData map[string]interface{}, hardcoded map[string]string) (interface{}, error) {
	jsonData := make(map[string]interface{})
	// Copy mapped data (show file info instead of base64)
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			jsonData[key] = formatFileContent(fh)
		} else if str, ok := val.(string); ok {
			jsonData[key] = str
		}
	}
	// Add hardcoded fields (giữ nguyên)
	for key, val := range hardcoded {
		jsonData[key] = val
	}
	return jsonData, nil
}

// buildJSONFromReq: Cập nhật với stream
func buildJSONFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (interface{}, error) {
	jsonData := make(map[string]interface{})
	// Copy mapped data (stream convert files to base64)
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			f, err := fh.Open()
			if err != nil {
				return nil, err
			}
			defer f.Close()

			// Stream: Sử dụng bufio.Reader để đọc chunk, encode base64 on-the-fly
			reader := bufio.NewReader(f)
			var b bytes.Buffer
			encoder := base64.NewEncoder(base64.StdEncoding, &b)
			if _, err := io.Copy(encoder, reader); err != nil {
				return nil, err
			}
			encoder.Close() // Flush base64

			jsonData[key] = b.String() // Base64 string
		} else if str, ok := val.(string); ok {
			jsonData[key] = str
		}
	}
	// Add hardcoded fields (giữ nguyên)
	for key, val := range hardcoded {
		jsonData[key] = val
	}
	return jsonData, nil
}

// buildMultipartFromReq: Build multipart từ mapped data + hardcoded
func buildMultipartFromReq(mappedData map[string]interface{}, hardcoded map[string]string) (io.Reader, string, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	// Add mapped files/text
	for key, val := range mappedData {
		if fh, ok := val.(*multipart.FileHeader); ok && fh != nil {
			fw, err := w.CreateFormFile(key, fh.Filename)
			if err != nil {
				return nil, "", err
			}
			f, err := fh.Open()
			if err != nil {
				return nil, "", err
			}
			defer f.Close()
			io.Copy(fw, f)
		} else if str, ok := val.(string); ok && str != "" {
			w.WriteField(key, str)
		}
	}
	// Add hardcoded as text fields
	for key, val := range hardcoded {
		w.WriteField(key, val)
	}
	w.Close()
	return &b, w.FormDataContentType(), nil
}

// ProxyHandler: Cập nhật với debug check
func (p *Proxy) ProxyHandler(c *gin.Context) {
	service := c.Param("service")
	upstream := p.balancer.GetUpstream(service)
	if upstream == nil {
		c.JSON(404, gin.H{"error": "No upstream found"})
		return
	}

	// Bind và validate (giữ nguyên)
	reqStruct := getRequestStruct(service)
	if reqStruct == nil {
		c.JSON(400, gin.H{"error": "Invalid service"})
		return
	}
	if err := c.ShouldBind(reqStruct); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// Áp dụng mapping
	mappedData := applyMappingToReq(reqStruct, upstream.FieldMapping)

	var body io.Reader
	var contentType string
	if upstream.Debug {
		// Build body for debug sample
		if upstream.Format == "json" {
			jsonData, err := buildJSONFromReqDebug(mappedData, upstream.HardcodedFields)
			if err != nil {
				c.JSON(400, gin.H{"error": err.Error()})
				return
			}
			jsonBytes, _ := json.Marshal(jsonData)
			body = bytes.NewReader(jsonBytes)
			contentType = "application/json"
		} else {
			var err error
			body, contentType, err = buildMultipartFromReq(mappedData, upstream.HardcodedFields)
			if err != nil {
				c.JSON(400, gin.H{"error": err.Error()})
				return
			}
		}

		// Return debug response
		debugResp := buildDebugResponse(c, service, reqStruct, upstream, mappedData, contentType, body)
		c.JSON(200, debugResp)
		return
	}

	if upstream.Format == "json" {
		jsonData, err := buildJSONFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		jsonBytes, _ := json.Marshal(jsonData)
		body = bytes.NewReader(jsonBytes)
		contentType = "application/json"
	} else {
		var err error
		body, contentType, err = buildMultipartFromReq(mappedData, upstream.HardcodedFields)
		if err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
	}

	// Tạo req và proxy (giữ nguyên logic cũ)
	u, _ := url.Parse(upstream.URL)
	req, _ := http.NewRequest(c.Request.Method, upstream.URL, body)
	req.Header.Set("Content-Type", contentType)
	for key, values := range c.Request.Header {
		if key != "Content-Length" {
			for _, value := range values {
				req.Header.Add(key, value)
			}
		}
	}

	proxy := httputil.NewSingleHostReverseProxy(u)
	proxy.Transport = &http.Transport{}
	originalDirector := proxy.Director
	proxy.Director = func(r *http.Request) {
		originalDirector(r)
		*r = *req
		r.URL = u
	}
	proxy.ServeHTTP(c.Writer, req)
}
