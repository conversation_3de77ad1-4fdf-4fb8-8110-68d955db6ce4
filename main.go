package main

import (
	"encoding/json"
	"os"

	"github.com/gin-gonic/gin"
)

func main() {
	r := gin.Default()
	balancer := NewBalancer()
	proxy := NewProxy(balancer)

	// Load config từ file (động: có thể thêm endpoint update)
	loadConfig(balancer)

	// Routes cho services
	r.POST("/:service", proxy.ProxyHandler) // /ocr, /liveness, /facematch

	// Endpoint admin để update config động (ví dụ)
	r.POST("/admin/config", func(c *gin.Context) {
		var cfg map[string]ServiceConfig
		if err := c.BindJSON(&cfg); err != nil {
			c.JSON(400, gin.H{"error": err.Error()})
			return
		}
		for service, config := range cfg {
			balancer.LoadConfig(service, &config)
		}
		c.<PERSON>(200, gin.H{"message": "Config updated"})
	})

	r.Run(":8080")
}

func loadConfig(b *Balancer) {
	data, err := os.ReadFile("config.json")
	if err != nil {
		panic(err)
	}
	var allConfigs map[string]ServiceConfig
	json.Unmarshal(data, &allConfigs)
	for service, cfg := range allConfigs {
		b.LoadConfig(service, &cfg)
	}
}
