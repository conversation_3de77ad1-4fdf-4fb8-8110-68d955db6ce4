package main

import (
	"log"

	"nova-proxy/internal/config"
	"nova-proxy/internal/handlers"
	"nova-proxy/internal/services"

	"github.com/gin-gonic/gin"
)

func main() {
	// Initialize services
	balancer := services.NewBalancer()
	proxyService := services.NewProxyService()
	debugService := services.NewDebugService()

	// Initialize configuration loader
	configLoader := config.NewLoader("config.json")

	// Load initial configuration
	if err := configLoader.LoadConfig(balancer); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize handlers
	proxyHandler := handlers.NewProxyHandler(balancer, proxyService, debugService)
	adminHandler := handlers.NewAdminHandler(balancer)

	// Setup router
	r := gin.Default()

	// Service routes
	r.POST("/:service", proxyHandler.HandleProxy) // /ocr, /liveness, /facematch

	// Admin routes
	adminGroup := r.Group("/admin")
	{
		adminGroup.POST("/config", adminHandler.UpdateConfig)
		adminGroup.GET("/config", adminHandler.GetConfig)
		adminGroup.GET("/status", adminHandler.GetServiceStatus)
	}

	// Start server
	log.Println("Starting nova-proxy server on :8080")
	r.Run(":8080")
}
